import 'localstorage-polyfill';
import React, { useEffect, useState, useCallback, useRef, useMemo, useLayoutEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { ApolloProvider } from 'react-apollo';
import NetInfo from '@react-native-community/netinfo';
import { ApolloProvider as ApolloHookProvider } from '@apollo/client';
import Mapbox from '@rnmapbox/maps';
import { Platform, NativeEventEmitter, NativeModules, AppState, Linking, Alert } from 'react-native';
import Toast, { ToastProps } from 'react-native-toast-message';
import RNFS from 'react-native-fs';
const CleverTap = require('clevertap-react-native');

// STEP 1: Import SafeAreaProvider
import { SafeAreaProvider } from 'react-native-safe-area-context';

import RenderNavigation from './src/navigation';
import useAuth from './src/hooks/useAuth';
import useApolloClient from './src/hooks/useApolloClient';
import { mapBoxAccessToken } from './src/screens/my-TG-Stream-Chat/client';
import ErrorBoundary from './src/components/ErrorBoundry/ErrorBoundry';
import constants from './src/utils/constants/constants';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { User } from './src/interface';
import CustomToastUI from './src/components/toast/CustomToastUI';
import CacheManager from './src/utils/cacheManager';
import ShareExtensionTest from './src/components/ShareExtensionTest';

// SIMPLE SOLUTION: Using deep links for share extension - no native module needed
console.log('� [DEEP LINK] Share Extension using deep links instead of native modules');

const App = React.memo(() => {
    const {
        user,
        token,
        initializing,
        refreshUser,
        refreshToken,
        needsAnnualConfirmation,
        deferAnnualConfirmation,
        setDeferAnnualConfirmation,
        streamClient,
        duesState,
        maintenance,
        clubValidationRequired,
    } = useAuth() as unknown as { user: User | null; [key: string]: any };

    const { SharedDataModule } = NativeModules;

    // SIMPLE SOLUTION: Deep links handle shared images - no native module needed

    // SIMPLE SOLUTION: Listen for deep links with shared images
    useEffect(() => {
        console.log('🔗 [DEEP LINK] Setting up deep link listener for shared images...');

        // Handle deep links when app is opened
        const handleDeepLink = (url: string) => {
            console.log('� [DEEP LINK] Received URL:', url);

            if (url.includes('thousandgreens://shared-images')) {
                console.log('🎯 [DEEP LINK] Shared images deep link detected!');
                handleSharedImagesFromDeepLink(url);
            }
        };

        // Listen for deep links
        const linkingSubscription = Linking.addEventListener('url', (event) => {
            handleDeepLink(event.url);
        });

        // Check if app was opened with a deep link
        Linking.getInitialURL().then((url) => {
            if (url) {
                console.log('🔗 [DEEP LINK] App opened with initial URL:', url);
                handleDeepLink(url);
            }
        });

        return () => {
            linkingSubscription?.remove();
        };
    }, []);

    const [isInternet, setInternet] = useState<boolean>(true);
    const [shareSheetImageUrl, setShareSheetImageUrl] = useState<string[]>([]);

               // iOS Standard Sharing state
           const [sharedImages, setSharedImages] = useState<string[]>([]);
           const [hasSharedContent, setHasSharedContent] = useState(false);

    const navigationRef = useRef(null);
    const appState = useRef(AppState.currentState);

    const client = useApolloClient(token);

    // MOVED: handleSharedImagesFromDeepLink will be defined after handleImage

    // Clear Apollo cache when cache is cleared
    useEffect(() => {
        if (client) {
            // Override the clearApolloCache method to use the actual client
            CacheManager.clearApolloCache = async () => {
                try {
                    // @ts-ignore - Apollo client has clearStore method
                    if (client && typeof client.clearStore === 'function') {
                        // @ts-ignore
                        await client.clearStore();
                        console.log('CacheManager: Apollo cache cleared successfully');
                    }
                } catch (error) {
                    console.error('CacheManager: Error clearing Apollo cache:', error);
                }
            };
        }
    }, [client]);

    // Check for app updates and clear cache if needed
    useEffect(() => {
        const checkForUpdates = async () => {
            try {
                await CacheManager.checkAndClearCacheOnUpdate();
            } catch (error) {
                console.error('Error checking for app updates:', error);
            }
        };
        
        checkForUpdates();
    }, []);

               // OLD CODE REMOVED: clearSharedImages function not needed with deep links

    useLayoutEffect(() => {
        console.log('🔄 [SHARE EXTENSION] Initializing shareSheetImageUrl to empty array');
        setShareSheetImageUrl([]);
    }, []);

    // Monitor changes to shareSheetImageUrl state
    useEffect(() => {
        console.log('📊 [SHARE EXTENSION] shareSheetImageUrl state changed:', {
            count: shareSheetImageUrl.length,
            images: shareSheetImageUrl,
            timestamp: new Date().toISOString()
        });

        if (shareSheetImageUrl.length > 0) {
            console.log('🎯 [SHARE EXTENSION] Image paths available for use in app:', shareSheetImageUrl);
        }
    }, [shareSheetImageUrl]);

    useEffect(() => {
        if (Platform.OS === 'android') {
            const sharedDataEmitter = new NativeEventEmitter(SharedDataModule);
            const subscription = sharedDataEmitter.addListener('SharedData', (event) => {
                convertContentUrisToFilePaths(event?.uris).then((localPath) => {
                    handleImage(localPath);
                });
            });

            return () => {
                subscription.remove(); // Cleanup listener on component unmount
            };
        }
    }, []);

    useEffect(() => {
        const fetchSharedData = async () => {
            try {
                const sharedData = await SharedDataModule.getSharedData();
                if (sharedData) {
                    convertContentUrisToFilePaths(sharedData?.uris).then((localPath) => {
                        handleImage(localPath);
                    });
                }
            } catch (error) {
                console.error('Error fetching shared data:', error);
            }
        };

        if (Platform.OS === 'android') fetchSharedData();
    }, []);

    const convertContentUrisToFilePaths = async (contentUris: string[]) => {
        try {
            if (!contentUris || contentUris.length === 0) {
                return [];
            }

            const convertedPaths = await Promise.all(
                contentUris.map(async (contentUri, index) => {
                    // Generate a unique filename using timestamp
                    const uniqueId = new Date().getTime() + Math.floor(Math.random() * 1000);
                    const destPath = `${RNFS.TemporaryDirectoryPath}/shared_image_${uniqueId}_${index}.jpg`;

                    await RNFS.copyFile(contentUri, destPath);
                    return destPath;
                }),
            );

            return convertedPaths;
        } catch (error) {
            console.error('Error converting content URIs:', error);
            return [];
        }
    };

    // Memoized Toast configuration
    const toastConfig = useMemo(
        () => ({
            // @ts-ignore
            error: (props: ToastProps) => <CustomToastUI props={props} />,
            // @ts-ignore
            success: (props: ToastProps) => <CustomToastUI props={props} />,
        }),
        [],
    );

    // Handle shared image - SAME FUNCTION FOR BOTH iOS AND ANDROID
    const handleImage = useCallback((data: string[] = []) => {
        try {
            console.log('🖼️ [SHARE EXTENSION] handleImage called with data:', {
                dataLength: data.length,
                imagePaths: data,
                timestamp: new Date().toISOString()
            });

            // Log each image path individually for better debugging
            data.forEach((imagePath, index) => {
                console.log(`🖼️ [SHARE EXTENSION] Image ${index + 1}:`, {
                    path: imagePath,
                    isBase64: imagePath.startsWith('data:image/'),
                    isFilePath: imagePath.startsWith('file://') || imagePath.startsWith('/'),
                    pathLength: imagePath.length
                });
            });

            setShareSheetImageUrl(data);
            console.log('🖼️ [SHARE EXTENSION] shareSheetImageUrl state updated successfully');
        } catch (e) {
            console.error('❌ [SHARE EXTENSION] Error in handleImage:', e);
        }
    }, []);

    // UNIFIED iOS IMPLEMENTATION - SAME AS ANDROID
    const handleSharedImagesFromDeepLink = useCallback((url: string) => {
        console.log('🎯 [iOS SHARE] Processing shared images from URL:', url);

        try {
            const urlObj = new URL(url);
            const params = urlObj.searchParams;
            const count = parseInt(params.get('count') || '0');

            console.log('🎯 [iOS SHARE] Found', count, 'shared images');

            if (count > 0) {
                const imagePaths: string[] = [];

                // Extract image paths from URL parameters
                for (let i = 0; i < count; i++) {
                    const imagePath = params.get(`image${i}`);
                    if (imagePath) {
                        imagePaths.push(imagePath);
                        console.log(`🎯 [iOS SHARE] Image ${i + 1}:`, imagePath);
                    }
                }

                if (imagePaths.length > 0) {
                    console.log('✅ [iOS SHARE] Successfully extracted', imagePaths.length, 'image paths');

                    // SAME AS ANDROID: Use the existing handleImage function
                    // This ensures iOS follows the exact same flow as Android
                    handleImage(imagePaths);

                    console.log('🎉 [iOS SHARE] Images processed using same flow as Android!');
                } else {
                    console.log('⚠️ [iOS SHARE] No valid image paths found');
                }
            } else {
                console.log('⚠️ [iOS SHARE] No images found in deep link');
            }
        } catch (error) {
            console.error('❌ [iOS SHARE] Error processing shared images:', error);
        }
    }, [handleImage]);

               // OLD CODE REMOVED: Using deep links instead of these functions

           // OLD CODE REMOVED: Using deep links instead of App Group

           // OLD CODE REMOVED: Now using deep links instead of native module



           // OLD CODE REMOVED: Now using deep links instead of App State listeners

    // NetInfo listener and Mapbox configuration
    useEffect(() => {
        const unsubscribe = NetInfo.addEventListener((state) => {
            if (state.isConnected !== isInternet) {
                setInternet(state.isConnected ?? true);
            }
        });

        Mapbox.setAccessToken(mapBoxAccessToken);
        Mapbox.setTelemetryEnabled(false);

        return () => unsubscribe();
    }, [isInternet]);

    // CleverTap setup
    useEffect(() => {
        if (user?.id) {
            CleverTap.getCleverTapID((err: Error | null, res: string) => {
                if (err) console.error('CleverTapID Error:', err);
                else console.log('CleverTapID:', res);
            });

            CleverTap.setDebugLevel(3);
            CleverTap.onUserLogin({
                [constants.CLEVERTAP.PROFILE.IDENTITY]: user?.id,
                [constants.CLEVERTAP.PROFILE.NAME]: `${user?.first_name} ${user?.last_name}`,
                [constants.CLEVERTAP.PROFILE.EMAIL]: user?.email,
            });
        }
    }, [user?.id]);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <ErrorBoundary>
                <NavigationContainer ref={navigationRef}>
                    {client && (
                        <ApolloProvider client={client}>
                            <ApolloHookProvider client={client}>
                                {/* STEP 2: Wrap your navigation stack with the provider */}
                                <SafeAreaProvider>
                                    <RenderNavigation
                                        navigationRef={navigationRef}
                                        duesState1={duesState}
                                        shareSheetImageUrl={shareSheetImageUrl}
                                        user={user}
                                        token={token}
                                        initializing={initializing}
                                        refreshUser={refreshUser}
                                        refreshToken={refreshToken}
                                        needsAnnualConfirmation={needsAnnualConfirmation}
                                        deferAnnualConfirmation={deferAnnualConfirmation}
                                        setDeferAnnualConfirmation={setDeferAnnualConfirmation}
                                        streamClient={streamClient}
                                        maintenance={maintenance}
                                        clubValidationRequired={clubValidationRequired}
                                    />
                                    
                                    <Toast config={toastConfig} />
                                </SafeAreaProvider>
                            </ApolloHookProvider>
                        </ApolloProvider>
                    )}
                </NavigationContainer>
            </ErrorBoundary>
        </GestureHandlerRootView>
    );
});

export default App;
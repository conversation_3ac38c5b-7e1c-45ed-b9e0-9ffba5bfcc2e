# Cache Management Feature

## Overview

This feature automatically clears cached data when the app is updated, preventing stale cache issues that cause the app to appear "bricked" on Android devices.

## How It Works

### Automatic Cache Clearing
- The app checks for version changes on every launch
- If the app version has changed, all cached data is automatically cleared
- Essential data (user info, device ID, admin status) is preserved
- Apollo GraphQL cache is also cleared

### Manual Cache Clearing
- Users can manually clear cache from Settings > Clear Cache
- This provides an option for users experiencing issues

## Implementation Details

### Files Modified/Created

1. **`src/utils/cacheManager.ts`** (New)
   - Main cache management utility
   - Handles version checking and cache clearing
   - Preserves essential data while clearing others

2. **`App.tsx`** (Modified)
   - Integrated cache manager on app launch
   - Added Apollo cache clearing capability

3. **`src/hooks/useApolloClient.js`** (Modified)
   - Added cache clearing support for GraphQL cache

4. **`src/screens/setting/view/Setting.tsx`** (Modified)
   - Added manual cache clearing option in settings

### Cache Clearing Strategy

The cache manager clears the following:
- **AsyncStorage**: All non-essential cached data
- **Apollo GraphQL Cache**: All GraphQL query cache
- **File System Cache**: Temporary files and images

**Preserved Data:**
- App version tracking
- User authentication data
- Device ID
- Admin status
- iOS launch flags

## Benefits

1. **Prevents "Bricked" App Issues**: Automatically resolves stale cache problems
2. **Improved User Experience**: Users no longer need to manually delete and reinstall
3. **Better Performance**: Fresh cache after updates ensures optimal performance
4. **Manual Override**: Users can manually clear cache if needed

## Testing

To test the feature:
1. Update the app version in `package.json` or build files
2. Launch the app
3. Check console logs for cache clearing messages
4. Verify that essential data is preserved

## Future Enhancements

- Add cache size monitoring
- Implement selective cache clearing based on update type
- Add cache analytics and reporting 
# 🚀 Share Extension Setup Guide for TG Project

## 📱 What's Been Implemented

### ✅ React Native Code
- **App.tsx**: Updated with iOS share extension integration
- **ShareExtensionModule.ts**: TypeScript interface for the native module
- **ShareExtensionTest.tsx**: Test component to verify functionality

### ✅ iOS Native Code
- **ShareViewController.swift**: Share extension logic for processing shared images
- **ShareExtensionModule.m**: Native module for React Native to access shared data
- **Info.plist**: Updated with proper share extension configuration
- **Entitlements**: App Groups already configured

### ✅ Android Native Code
- **ShareDataModule.kt**: Already implemented and working
- **AndroidManifest.xml**: Intent filters already configured

## 🔧 Xcode Configuration Required

### 1. Add ShareExtensionModule.m to Main App Target
1. Open `ios/thousandgreens.xcworkspace` in Xcode
2. Right-click on the main app target (thousandgreens)
3. Select "Add Files to 'thousandgreens'"
4. Navigate to and select `ios/thousandgreens/ShareExtensionModule.m`
5. Ensure it's added to the main app target (not the share extension target)

### 2. Verify Share Extension Target Configuration
1. In Xcode, select the `TGShareExtension` target
2. Go to "Build Phases" → "Compile Sources"
3. Ensure `ShareViewController.swift` is listed
4. Go to "Build Settings" → "Swift Language Version" → Set to "Swift 5.0"

### 3. Verify App Groups Configuration
1. Select main app target → "Signing & Capabilities"
2. Verify "App Groups" capability is enabled
3. Verify group ID: `group.com.thousand-greens`
4. Select share extension target → "Signing & Capabilities"
5. Verify same App Group ID: `group.com.thousand-greens`

### 4. Verify Bundle Identifiers
- **Main App**: `com.thousand-greens` (or your actual bundle ID)
- **Share Extension**: `com.thousand-greens.TGShareExtension` (or similar)

### 5. Verify URL Scheme
- Main app Info.plist should have URL scheme: `thousandgreens`
- This allows the share extension to open the main app

## 🧪 Testing the Share Extension

### Test Steps
1. Build and run your app in Xcode
2. Open Photos app on the device/simulator
3. Select an image
4. Tap Share button
5. Choose "TGShareExtension" from the share sheet
6. Tap "Post"
7. App should open automatically
8. Check the test component for shared image status

### Debug Logs
Check Xcode console for:
- `ShareExtension:` messages
- `ShareExtensionModule:` messages
- App state change messages

## ⚠️ Common Issues & Solutions

### Issue 1: Build Errors
- **Solution**: Ensure `ShareExtensionModule.m` is added to main app target
- **Check**: Build Phases → Compile Sources

### Issue 2: App Groups Not Working
- **Solution**: Verify both targets have same App Group ID
- **Check**: Signing & Capabilities → App Groups

### Issue 3: URL Scheme Not Working
- **Solution**: Verify URL scheme in main app Info.plist
- **Check**: URL Types → URL Schemes

### Issue 4: Images Not Appearing
- **Solution**: Check App Group permissions
- **Check**: Console logs for errors

## 🎯 Key Points to Remember

1. **App Groups must match** between main app and extension
2. **Bundle identifiers** must be properly configured
3. **URL scheme** must be unique and match the one in ShareViewController
4. **File linking** must be correct in Xcode
5. **Entitlements** must be enabled for both targets

## 📚 Files Modified/Created

### New Files
- `src/ShareExtensionModule.ts`
- `src/components/ShareExtensionTest.tsx`
- `ios/thousandgreens/ShareExtensionModule.m`
- `SHARE_EXTENSION_SETUP.md`

### Modified Files
- `App.tsx` - Added iOS share extension integration
- `ios/TGShareExtension/ShareViewController.swift` - Updated with working logic
- `ios/TGShareExtension/Info.plist` - Updated configuration
- `ios/thousandgreens/Info.plist` - Added URL scheme

## 🚀 Next Steps

1. **Open Xcode** and add the native module file
2. **Build and test** the share extension
3. **Remove test component** when ready for production
4. **Test on real device** (share extension may not work in simulator)

---

**Note**: The Android share extension is already working. This implementation adds iOS share extension functionality to match the working reference implementation. 
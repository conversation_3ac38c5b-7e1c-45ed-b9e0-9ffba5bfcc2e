# 🚀 Share Extension Xcode Setup Guide - Fix Build Cycle Error

## 🚨 **IMMEDIATE FIX FOR BUILD CYCLE ERROR**

### **Step 1: Open Xcode Project**
1. Open `ios/thousandgreens.xcworkspace` (NOT .xcodeproj)
2. Wait for X<PERSON> to fully load the project

### **Step 2: Fix Build Phases Order (CRITICAL)**
1. Select the **main app target** (thousandgreens) in the left sidebar
2. Go to **Build Phases** tab
3. **Drag and reorder** the phases in this EXACT order:

```
1. Headers
2. Compile Sources
3. Link Binary With Libraries
4. Copy Bundle Resources
5. [CP] Embed Pods Frameworks
6. [CP-User] [RNFB] Core Configuration
7. Embed App Extensions ← MUST BE LAST
```

**⚠️ IMPORTANT**: The "Embed App Extensions" phase MUST be the very last phase!

### **Step 3: Fix Embed App Extensions Phase**
1. In the **"Embed App Extensions"** phase:
   - Click the **+** button
   - Select `TGShareExtension.appex`
   - Set **Destination** to `PlugIns`
   - Set **Code Sign on Copy** to `YES`

### **Step 4: Add ShareExtensionModule.m to Main Target**
1. Right-click on the **main app target** (thousandgreens)
2. Select **"Add Files to 'thousandgreens'"**
3. Navigate to `ios/thousandgreens/ShareExtensionModule.m`
4. **Ensure it's added to the main app target** (not the share extension target)
5. Click **"Add"**

### **Step 5: Verify Target Dependencies**
1. In the main app target, go to **Build Phases** → **Target Dependencies**
2. Ensure `TGShareExtension` is listed here
3. If not, click **+** and add it

### **Step 6: Fix Share Extension Target Settings**
1. Select the **TGShareExtension** target
2. Go to **Build Settings**
3. Search for **"Skip Install"** and set it to **"Yes"**
4. Search for **"Installation Directory"** and set it to **"$(CONTENTS_FOLDER_PATH)/PlugIns"**

### **Step 7: Verify App Groups Configuration**
1. Select **main app target** → **Signing & Capabilities**
2. Verify **App Groups** capability is enabled
3. Verify group ID: `group.com.thousand-greens`
4. Select **share extension target** → **Signing & Capabilities**
5. Verify same App Group ID: `group.com.thousand-greens`

### **Step 8: Clean and Rebuild**
1. **Product** → **Clean Build Folder** (Shift + Cmd + K)
2. **Product** → **Build** (Cmd + B)

## 🔧 **ALTERNATIVE SOLUTION - If Build Cycle Persists**

### **Option A: Disable Parallel Builds**
1. **Product** → **Scheme** → **Edit Scheme**
2. Select **Build** on the left
3. Uncheck **"Find Implicit Dependencies"**
4. Uncheck **"Parallelize Build"**

### **Option B: Fix Build Phase Dependencies**
1. In **Build Phases**, ensure no phase depends on another
2. Check that **"Embed App Extensions"** doesn't have input/output files that conflict

### **Option C: Recreate Share Extension Target**
If still having issues:
1. **Delete the existing TGShareExtension target**
2. **Create a new Share Extension**:
   - File → New → Target
   - Choose **"Share Extension"**
   - Name: `TGShareExtension`
   - Language: **Swift**
3. **Copy our files back**:
   - Replace `ShareViewController.swift`
   - Replace `Info.plist`
   - Add `ShareExtensionModule.m` to main target

## 📱 **VERIFY SHARE EXTENSION CONFIGURATION**

### **Check Share Extension Target**
1. Select `TGShareExtension` target
2. Go to **Build Phases** → **Compile Sources**
3. Ensure `ShareViewController.swift` is listed
4. Go to **Build Settings** → **Swift Language Version** → Set to **"Swift 5.0"**

### **Check Main App Target**
1. Select main app target
2. Go to **Build Phases** → **Compile Sources**
3. Ensure `ShareExtensionModule.m` is listed
4. Go to **Build Phases** → **Embed App Extensions**
5. Ensure `TGShareExtension.appex` is listed

## 🧪 **TESTING THE SHARE EXTENSION**

### **Test Steps**
1. **Build and run** your app in Xcode
2. Open **Photos app** on device/simulator
3. Select an **image**
4. Tap **Share button**
5. Choose **"TGShareExtension"** from share sheet
6. Tap **"Post"**
7. App should **open automatically**
8. Check the **test component** for shared image status

### **Debug Logs**
Check Xcode console for:
- `ShareExtension:` messages
- `ShareExtensionModule:` messages
- App state change messages

## ⚠️ **COMMON ISSUES & SOLUTIONS**

### **Issue 1: Build Errors**
- **Solution**: Ensure `ShareExtensionModule.m` is added to main app target
- **Check**: Build Phases → Compile Sources

### **Issue 2: App Groups Not Working**
- **Solution**: Verify both targets have same App Group ID
- **Check**: Signing & Capabilities → App Groups

### **Issue 3: URL Scheme Not Working**
- **Solution**: Verify URL scheme in main app Info.plist
- **Check**: URL Types → URL Schemes

### **Issue 4: Images Not Appearing**
- **Solution**: Check App Group permissions
- **Check**: Console logs for errors

## 🎯 **KEY POINTS TO REMEMBER**

1. **Build phase order is CRITICAL** - Embed App Extensions must be last
2. **App Groups must match** between main app and extension
3. **File linking must be correct** in Xcode
4. **Target dependencies must be set** correctly
5. **Clean build folder** after making changes

## 📚 **FILES TO VERIFY**

### **New Files Created**
- `ios/thousandgreens/ShareExtensionModule.m` ← Must be in main target
- `src/ShareExtensionModule.ts`
- `src/components/ShareExtensionTest.tsx`

### **Modified Files**
- `ios/TGShareExtension/ShareViewController.swift`
- `ios/TGShareExtension/Info.plist`
- `ios/thousandgreens/Info.plist`
- `App.tsx`

## 🚀 **NEXT STEPS AFTER XCODE SETUP**

1. **Build the project** successfully
2. **Test the share extension** functionality
3. **Remove test component** when ready for production
4. **Test on real device** (share extension may not work in simulator)

---

**Note**: The build cycle error is almost always caused by incorrect build phase ordering. Following the exact order above should resolve the issue. 
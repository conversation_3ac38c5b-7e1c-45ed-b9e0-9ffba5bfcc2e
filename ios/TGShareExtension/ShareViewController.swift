//
//  ShareViewController.swift
//  TGShareExtension
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 20/08/25.
//

import UIKit
import Social
import MobileCoreServices
import UniformTypeIdentifiers

class ShareViewController: SLComposeServiceViewController {

    let appGroupIdentifier = "group.com.thousand-greens"

    override func viewDidLoad() {
        super.viewDidLoad()

        // Set the title and placeholder
        self.title = "Share to TG"
        self.placeholder = "Add a comment..."

        // Customize the appearance
        self.navigationController?.navigationBar.tintColor = UIColor.systemBlue
    }

    override func isContentValid() -> Bool {
        // Always return true to allow sharing
        return true
    }

    override func didSelectPost() {
        print("ShareExtension: ===== didSelectPost called =====")

        // Process the shared content
        self.processSharedContent { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    print("ShareExtension: ✅ Successfully processed shared content")
                    // The processSharedContent method now handles opening the main app

                    // Add a small delay before completing to ensure the main app opens
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        print("ShareExtension: Completing extension request...")
                        self?.extensionContext?.completeRequest(returningItems: [], completionHandler: { _ in
                            print("ShareExtension: Extension request completed")
                        })
                    }
                } else {
                    print("ShareExtension: ❌ Failed to process shared content")
                    self?.extensionContext?.completeRequest(returningItems: [], completionHandler: { _ in
                        print("ShareExtension: Extension request completed (with failure)")
                    })
                }
            }
        }
    }

    private func processSharedContent(completion: @escaping (Bool) -> Void) {
        guard let extensionContext = self.extensionContext else {
            print("ShareExtension: No extension context")
            completion(false)
            return
        }

        let inputItems = extensionContext.inputItems
        var imagePaths: [String] = []
        let dispatchGroup = DispatchGroup()

        print("ShareExtension: Processing \(inputItems.count) input items")

        for inputItem in inputItems {
            guard let attachments = (inputItem as? NSExtensionItem)?.attachments else { continue }

            for attachment in attachments {
                dispatchGroup.enter()

                // Check if it's an image
                if attachment.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                    print("ShareExtension: Found image attachment")

                    attachment.loadItem(forTypeIdentifier: UTType.image.identifier, options: nil) { (item, error) in
                        defer { dispatchGroup.leave() }

                        if let error = error {
                            print("ShareExtension: Error loading image: \(error)")
                            return
                        }

                        var imageData: Data?

                        if let url = item as? URL {
                            print("ShareExtension: Loading image from URL: \(url)")
                            imageData = try? Data(contentsOf: url)
                        } else if let image = item as? UIImage {
                            print("ShareExtension: Converting UIImage to data")
                            imageData = image.jpegData(compressionQuality: 0.8)
                        } else if let data = item as? Data {
                            print("ShareExtension: Using direct data")
                            imageData = data
                        }

                        if let data = imageData {
                            print("ShareExtension: Successfully processed image data (\(data.count) bytes)")

                            // Save image to temporary directory and get file path
                            if let imagePath = self.saveImageToTempDirectory(data) {
                                imagePaths.append(imagePath)
                                print("ShareExtension: Saved image to: \(imagePath)")
                            }
                        } else {
                            print("ShareExtension: Failed to get image data")
                        }
                    }
                } else {
                    dispatchGroup.leave()
                }
            }
        }

        dispatchGroup.notify(queue: .main) {
            print("ShareExtension: ===== PROCESSING COMPLETE =====")
            print("ShareExtension: Found \(imagePaths.count) images")
            print("ShareExtension: Image paths: \(imagePaths)")

            if !imagePaths.isEmpty {
                print("ShareExtension: ✅ Calling openMainAppWithImagePaths with \(imagePaths.count) images")
                self.openMainAppWithImagePaths(imagePaths)
                completion(true)
            } else {
                print("ShareExtension: ❌ No images to process")
                completion(false)
            }
        }
    }

    private func saveImageToTempDirectory(_ imageData: Data) -> String? {
        // FINAL FIX: Use App Group - the ONLY way to share files between extension and main app
        let appGroupIdentifier = "group.com.thousand-greens.shared"

        print("ShareExtension: 🔧 FINAL FIX - Using App Group: \(appGroupIdentifier)")

        guard let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            print("ShareExtension: ❌ CRITICAL: App Group '\(appGroupIdentifier)' not accessible!")
            print("ShareExtension: ❌ Check if App Group is enabled in:")
            print("ShareExtension: ❌ 1. Main app target capabilities")
            print("ShareExtension: ❌ 2. Share Extension target capabilities")
            print("ShareExtension: ❌ 3. Apple Developer Portal")
            return nil
        }

        print("ShareExtension: ✅ App Group container: \(containerURL.path)")

        let sharedImagesDir = containerURL.appendingPathComponent("TGSharedImages")
        print("ShareExtension: 📁 Shared directory: \(sharedImagesDir.path)")

        // Create directory
        do {
            try FileManager.default.createDirectory(at: sharedImagesDir, withIntermediateDirectories: true, attributes: nil)
            print("ShareExtension: ✅ Shared directory ready")
        } catch {
            print("ShareExtension: ❌ Directory creation failed: \(error)")
            return nil
        }

        // Save image with unique name
        let timestamp = Int(Date().timeIntervalSince1970)
        let uuid = UUID().uuidString.prefix(8)
        let fileName = "shared_\(timestamp)_\(uuid).jpg"
        let fileURL = sharedImagesDir.appendingPathComponent(fileName)

        print("ShareExtension: 💾 Saving to: \(fileURL.path)")

        do {
            try imageData.write(to: fileURL)

            // Verify the file was saved
            let fileExists = FileManager.default.fileExists(atPath: fileURL.path)
            let fileSize = (try? FileManager.default.attributesOfItem(atPath: fileURL.path)[.size] as? Int) ?? 0

            print("ShareExtension: ✅ File saved successfully!")
            print("ShareExtension: ✅ Path: \(fileURL.path)")
            print("ShareExtension: ✅ Exists: \(fileExists)")
            print("ShareExtension: ✅ Size: \(fileSize) bytes")

            return fileURL.path
        } catch {
            print("ShareExtension: ❌ Save failed: \(error)")
            return nil
        }
    }

    private func openMainAppWithImagePaths(_ imagePaths: [String]) {
        print("ShareExtension: Opening main app with \(imagePaths.count) image paths")

        // Create deep link URL with image paths as parameters
        var urlComponents = URLComponents()
        urlComponents.scheme = "thousandgreens"
        urlComponents.host = "shared-images"

        // Add image paths as query parameters
        var queryItems: [URLQueryItem] = []
        for (index, path) in imagePaths.enumerated() {
            queryItems.append(URLQueryItem(name: "image\(index)", value: path))
        }
        queryItems.append(URLQueryItem(name: "count", value: "\(imagePaths.count)"))
        queryItems.append(URLQueryItem(name: "timestamp", value: "\(Date().timeIntervalSince1970)"))

        urlComponents.queryItems = queryItems

        guard let deepLinkURL = urlComponents.url else {
            print("ShareExtension: Failed to create deep link URL")
            return
        }

        print("ShareExtension: Deep link URL: \(deepLinkURL)")

        // FIXED: Use the proper method to open main app from Share Extension
        if #available(iOS 14.0, *) {
            // iOS 14+ method
            self.extensionContext?.open(deepLinkURL) { success in
                print("ShareExtension: Open main app result (iOS 14+): \(success)")
                if !success {
                    print("ShareExtension: Failed to open main app, trying alternative method...")
                    self.openMainAppAlternative(deepLinkURL)
                }
            }
        } else {
            // Fallback for older iOS versions
            self.openMainAppAlternative(deepLinkURL)
        }
    }

    private func openMainAppAlternative(_ url: URL) {
        print("ShareExtension: Trying alternative method to open main app...")

        // Method 1: Try using UIApplication if available
        var responder: UIResponder? = self
        while responder != nil {
            if let application = responder as? UIApplication {
                print("ShareExtension: Found UIApplication, attempting to open URL...")
                application.open(url, options: [:]) { success in
                    print("ShareExtension: UIApplication.open result: \(success)")
                }
                return
            }
            responder = responder?.next
        }

        // Method 2: Use NSUserActivity as fallback
        print("ShareExtension: UIApplication not found, using NSUserActivity...")
        let userActivity = NSUserActivity(activityType: "com.thousand-greens.share-images")
        userActivity.webpageURL = url
        userActivity.title = "Shared Images"
        userActivity.userInfo = ["url": url.absoluteString]

        self.extensionContext?.completeRequest(returningItems: [userActivity], completionHandler: nil)
    }

    override func configurationItems() -> [Any]! {
        // Return empty array - no additional configuration needed
        return []
    }
}

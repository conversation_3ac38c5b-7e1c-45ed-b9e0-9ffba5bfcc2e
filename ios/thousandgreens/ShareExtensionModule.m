#import <React/RCTBridgeModule.h>
#import <Foundation/Foundation.h>

@interface ShareExtensionModule : NSObject <RCTBridgeModule>
@end

@implementation ShareExtensionModule

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup {
  return NO;
}

RCT_EXPORT_METHOD(getSharedImages:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  NSString *appGroupIdentifier = @"group.com.thousand-greens";

  NSLog(@"ShareExtensionModule: getSharedImages - Attempting to get shared images from App Group: %@", appGroupIdentifier);

  NSUserDefaults *userDefaults = [[NSUserDefaults alloc] initWithSuiteName:appGroupIdentifier];
  if (!userDefaults) {
    NSLog(@"ShareExtensionModule: getSharedImages - ERROR: Could not access App Group UserDefaults");
    reject(@"ERROR", @"Could not access App Group", nil);
    return;
  }

  // Debug: List all keys in UserDefaults
  NSDictionary *allKeys = [userDefaults dictionaryRepresentation];
  NSLog(@"ShareExtensionModule: getSharedImages - All keys in App Group: %@", [allKeys allKeys]);

  NSArray *imagesData = [userDefaults arrayForKey:@"sharedImages"];
  NSNumber *timestamp = [userDefaults objectForKey:@"lastSharedTimestamp"];

  NSLog(@"ShareExtensionModule: getSharedImages - Raw imagesData: %@", imagesData);
  NSLog(@"ShareExtensionModule: getSharedImages - Timestamp: %@", timestamp);

  if (!imagesData || imagesData.count == 0) {
    NSLog(@"ShareExtensionModule: getSharedImages - No shared images found or empty array");
    resolve(@[]);
    return;
  }

  NSLog(@"ShareExtensionModule: getSharedImages - Found %lu shared images", (unsigned long)imagesData.count);

  NSMutableArray *base64Images = [NSMutableArray array];

  for (int i = 0; i < imagesData.count; i++) {
    NSData *imageData = imagesData[i];
    NSLog(@"ShareExtensionModule: getSharedImages - Processing image %d: %lu bytes", i, (unsigned long)imageData.length);

    if ([imageData isKindOfClass:[NSData class]] && imageData.length > 0) {
      NSString *base64String = [imageData base64EncodedStringWithOptions:0];
      NSLog(@"ShareExtensionModule: getSharedImages - Converted image %d to base64: %lu characters", i, (unsigned long)base64String.length);
      [base64Images addObject:base64String];
    } else {
      NSLog(@"ShareExtensionModule: getSharedImages - WARNING: Image %d is not valid NSData or is empty", i);
    }
  }

  // Clear the shared images after retrieving them
  [userDefaults removeObjectForKey:@"sharedImages"];
  [userDefaults removeObjectForKey:@"lastSharedTimestamp"];
  [userDefaults synchronize];
  NSLog(@"ShareExtensionModule: getSharedImages - Cleared shared images from App Group");

  NSLog(@"ShareExtensionModule: getSharedImages - Returning %lu base64 images", (unsigned long)base64Images.count);
  resolve(base64Images);
}

RCT_EXPORT_METHOD(hasSharedImages:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  NSString *appGroupIdentifier = @"group.com.thousand-greens";

  NSLog(@"ShareExtensionModule: hasSharedImages - Checking App Group: %@", appGroupIdentifier);

  NSUserDefaults *userDefaults = [[NSUserDefaults alloc] initWithSuiteName:appGroupIdentifier];
  if (!userDefaults) {
    NSLog(@"ShareExtensionModule: hasSharedImages - ERROR: Could not access App Group UserDefaults");
    resolve(@NO);
    return;
  }

  NSArray *sharedImages = [userDefaults objectForKey:@"sharedImages"];
  NSNumber *timestamp = [userDefaults objectForKey:@"lastSharedTimestamp"];

  BOOL hasImages = sharedImages != nil;
  NSLog(@"ShareExtensionModule: hasSharedImages - Result: %@, Images count: %lu, Timestamp: %@",
        hasImages ? @"YES" : @"NO",
        (unsigned long)(sharedImages ? sharedImages.count : 0),
        timestamp);

  resolve(@(hasImages));
}

RCT_EXPORT_METHOD(clearSharedImages:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  NSString *appGroupIdentifier = @"group.com.thousand-greens";

  NSUserDefaults *userDefaults = [[NSUserDefaults alloc] initWithSuiteName:appGroupIdentifier];
  if (!userDefaults) {
    reject(@"ERROR", @"Could not access App Group", nil);
    return;
  }

  [userDefaults removeObjectForKey:@"sharedImages"];
  [userDefaults removeObjectForKey:@"lastSharedTimestamp"];
  [userDefaults synchronize];

  resolve(@YES);
}

RCT_EXPORT_METHOD(testAppGroup:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
  NSString *appGroupIdentifier = @"group.com.thousand-greens";

  NSLog(@"ShareExtensionModule: testAppGroup - Testing App Group access: %@", appGroupIdentifier);

  NSUserDefaults *userDefaults = [[NSUserDefaults alloc] initWithSuiteName:appGroupIdentifier];
  if (!userDefaults) {
    NSLog(@"ShareExtensionModule: testAppGroup - ERROR: Could not access App Group UserDefaults");
    resolve(@{@"success": @NO, @"error": @"Could not access App Group"});
    return;
  }

  // Test write
  [userDefaults setObject:@"test-value" forKey:@"test-key"];
  [userDefaults synchronize];

  // Test read
  NSString *testValue = [userDefaults stringForKey:@"test-key"];
  BOOL canReadWrite = [testValue isEqualToString:@"test-value"];

  // Clean up
  [userDefaults removeObjectForKey:@"test-key"];
  [userDefaults synchronize];

  NSLog(@"ShareExtensionModule: testAppGroup - Can read/write: %@", canReadWrite ? @"YES" : @"NO");

  resolve(@{
    @"success": @(canReadWrite),
    @"appGroupId": appGroupIdentifier,
    @"canAccess": @YES
  });
}

@end 
import { NativeModules } from 'react-native';

const { ShareExtensionModule } = NativeModules;

interface ShareExtensionInterface {
  getSharedImages(): Promise<string[]>;
  hasSharedImages(): Promise<boolean>;
  clearSharedImages(): Promise<boolean>;
  testAppGroup(): Promise<{success: boolean; appGroupId: string; canAccess: boolean; error?: string}>;
}

export default ShareExtensionModule as ShareExtensionInterface; 
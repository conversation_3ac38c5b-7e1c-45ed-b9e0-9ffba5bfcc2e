import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, Platform, NativeModules } from 'react-native';

interface ShareExtensionTestProps {
  hasSharedContent: boolean;
  sharedImages: string[];
  isChecking: boolean;
  onCheckImages: () => void;
  onClearImages: () => void;
}

const ShareExtensionTest: React.FC<ShareExtensionTestProps> = ({
  hasSharedContent,
  sharedImages,
  isChecking,
  onCheckImages,
  onClearImages,
}) => {
  if (Platform.OS !== 'ios') {
    return null; // Only show on iOS
  }

  // Direct test function
  const testDirectly = async () => {
    try {
      const ShareExtensionModule = NativeModules.ShareExtensionModule;
      if (!ShareExtensionModule) {
        Alert.alert('Error', 'ShareExtensionModule not found');
        return;
      }

      console.log('🧪 [DIRECT TEST] Testing ShareExtensionModule directly...');

      // Test App Group access
      const appGroupTest = await ShareExtensionModule.testAppGroup();
      console.log('🧪 [DIRECT TEST] App Group test:', appGroupTest);

      // Check for shared images
      const hasImages = await ShareExtensionModule.hasSharedImages();
      console.log('🧪 [DIRECT TEST] Has shared images:', hasImages);

      if (hasImages) {
        const images = await ShareExtensionModule.getSharedImages();
        console.log('🧪 [DIRECT TEST] Retrieved images:', {
          count: images ? images.length : 0,
          type: typeof images,
          isArray: Array.isArray(images)
        });

        Alert.alert(
          'Direct Test Result',
          `Found ${images ? images.length : 0} shared images!\n\nCheck console for details.`
        );
      } else {
        Alert.alert('Direct Test Result', 'No shared images found');
      }
    } catch (error) {
      console.error('🧪 [DIRECT TEST] Error:', error);
      Alert.alert('Direct Test Error', error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Share Extension Test</Text>
      
      <TouchableOpacity
        style={styles.button}
        onPress={onCheckImages}
        disabled={isChecking}
      >
        <Text style={styles.buttonText}>
          {isChecking ? 'Checking...' : 'Check for Shared Images'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.testButton]}
        onPress={testDirectly}
      >
        <Text style={styles.buttonText}>
          🧪 Direct Test
        </Text>
      </TouchableOpacity>

      {hasSharedContent && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>
            Has shared content: {hasSharedContent ? 'Yes' : 'No'}
          </Text>
          <Text style={styles.statusText}>
            Number of images: {sharedImages.length}
          </Text>
        </View>
      )}

      {sharedImages.length > 0 && (
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={onClearImages}
        >
          <Text style={styles.buttonText}>Clear Shared Images</Text>
        </TouchableOpacity>
      )}

      <Text style={styles.instructions}>
        To test: Open Photos app → Select image → Share → Choose TGShareExtension → Post
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    margin: 10,
    borderRadius: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  testButton: {
    backgroundColor: '#34C759',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
  statusContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  statusText: {
    fontSize: 14,
    marginBottom: 5,
  },
  instructions: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    fontStyle: 'italic',
  },
});

export default ShareExtensionTest; 
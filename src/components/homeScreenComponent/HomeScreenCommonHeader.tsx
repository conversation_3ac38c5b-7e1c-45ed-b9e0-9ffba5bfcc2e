import { Image, ImageBackground, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext } from 'react';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { HomeScreenCommonHeaderProps } from '../../interface';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { AuthContext } from '../../context/AuthContext';
import { Back } from '../../assets/images/svg';
import {
    WhiteSearchIcon,
    RequestUpdatedIcon,
    WhiteTripleDot,
    CreateChatIcon,
    FilterWhiteIcon,
    HistoryClockIcon,
    MapIconWhite,
    MapListIconBlack,
    MapListIconWhite,
    MapIconBlack,
    TealFilter,
} from '../../assets/svg';
import config from '../../config';
import CleverTap from 'clevertap-react-native';
import constants from '../../utils/constants/constants';
import { tiers } from '../../utils/constants/constants';
import { GlobalContext } from '../../context/contextApi';
import StreamHeader from '../layout/StreamHeader';

const HomeScreenCommonHeader: React.FC<HomeScreenCommonHeaderProps> = ({
    title,
    onPressGoBack,
    showSearchIcon,
    handleSearchIcon,
    setActiveView,
    activeView,
    isFilterApplied,
    showFilter,
    openFilterModal,
    handleCreateRequest,
    showCreateRequestIcon,
    showTripleDot,
    handleToggleRecentPopup,
    showCreateChatIcon,
    handleCreateChat,
    handleRequestHistory,
    showRequestHistory,
    historyUnreadMessageStatus,
    hideToggleIcon = false,
}) => {
    const navigation = useNavigation<NavigationProp<any>>();
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const insets = useSafeAreaInsets();
    const handleProfilePress = () => {
        CleverTap.recordEvent(constants.CLEVERTAP.OWN_PROFILE_HEADER, {
            'User email': user?.email,
            'Current Account Status': user?.muted ? 'Muted' : 'Unmuted',
            Membership: user?.membership_plan?.name,
            Tier: tiers[user?.tier],
        });
        navigation.navigate(config.routes.SETTING);
    };

    return (
        <>
            <StatusBar barStyle="dark-content" />
            <ImageBackground
                source={require('../../assets/images/profileBG.png')}
                style={{
                    height: Size.SIZE_100,
                    width: '100%',
                    paddingTop: Spacing.SCALE_40,
                }}>
                <View style={[styles.container]}>
                    <View
                        style={{
                            width: '100%',
                            alignItems: 'center',
                            height: 60,
                            flexDirection: 'row',
                            paddingHorizontal: 15,
                        }}>
                        {['Clubs', 'Home', 'Requests', 'Chat', 'Map', 'Offers'].includes(title) ? (
                            <TouchableOpacity style={styles.profileIcon} onPress={handleProfilePress}>
                                {user?.profilePhoto ? (
                                    <Image source={{ uri: user?.profilePhoto }} style={styles.profileIcon} />
                                ) : (
                                    <Text
                                        style={{
                                            fontSize: Typography.FONT_SIZE_17,
                                            color: colors.tealRgb,
                                            fontFamily: 'Ubuntu-Medium',
                                            textTransform: 'uppercase',
                                        }}>
                                        {user?.full_name[0]}
                                    </Text>
                                )}
                            </TouchableOpacity>
                        ) : (
                            <TouchableOpacity
                                onPress={() => {
                                    if (onPressGoBack) {
                                        onPressGoBack();
                                    } else {
                                        navigation.goBack();
                                    }
                                }}
                                style={{ zIndex: 100 }}>
                                <Back fill={colors.whiteRGB} height={25} />
                            </TouchableOpacity>
                        )}

                        {title === 'Home' ? (
                            <>
                                <TouchableOpacity style={{ marginLeft: Spacing.SCALE_12 }} onPress={handleProfilePress}>
                                    <Text style={styles.text}>{user?.full_name}</Text>
                                </TouchableOpacity>
                            </>
                        ) : (
                            <Text
                                style={{
                                    fontFamily: 'Ubuntu-Medium',
                                    fontSize: Typography.FONT_SIZE_18,
                                    textAlign: 'center',
                                    fontWeight: '500',
                                    color: colors.whiteRGB,
                                    lineHeight: Size.SIZE_20,
                                    marginLeft: Spacing.SCALE_20,
                                }}>
                                {title}
                            </Text>
                        )}

                        {['Clubs', 'Home', 'Requests', 'Chat', 'Map'].includes(title) && (
                            <View
                                style={{
                                    position: 'absolute',
                                    zIndex: 50,
                                    flexDirection: 'row',
                                    right: 15,
                                    alignItems: 'center',
                                }}>
                                {showSearchIcon && (
                                    <TouchableOpacity onPress={handleSearchIcon} style={{ marginRight: 15 }}>
                                        <WhiteSearchIcon height={20} width={20} stroke={colors.whiteRGB} />
                                    </TouchableOpacity>
                                )}
                                {!hideToggleIcon && title === 'Map' && (
                                    <TouchableOpacity
                                        style={styles.mapToggle}
                                        onPress={() => {
                                            if (setActiveView && activeView) {
                                                setActiveView(activeView === 'MAP' ? 'LIST' : 'MAP');
                                            }
                                        }}>
                                        {activeView === 'MAP' ? (
                                            <View style={styles.mapToggleIconContainer}>
                                                <View style={styles.mapToggleIconTeal}>
                                                    <MapIconWhite />
                                                </View>
                                                <View style={styles.mapToggleIcon}>
                                                    <MapListIconBlack />
                                                </View>
                                            </View>
                                        ) : (
                                            <View style={styles.mapToggleIconContainer}>
                                                <View style={styles.mapToggleIcon}>
                                                    <MapIconBlack />
                                                </View>
                                                <View style={styles.mapToggleIconTeal}>
                                                    <MapListIconWhite />
                                                </View>
                                            </View>
                                        )}
                                    </TouchableOpacity>
                                )}

                                {showFilter && (
                                    <View>
                                        <TouchableOpacity
                                            style={[
                                                styles.filterIcon,
                                                { backgroundColor: isFilterApplied ? colors.whiteRGB : colors.tealRgb },
                                            ]}
                                            onPress={() => {
                                                if (openFilterModal) {
                                                    openFilterModal();
                                                }
                                            }}>
                                            {isFilterApplied ? <TealFilter /> : <FilterWhiteIcon />}
                                        </TouchableOpacity>
                                        {isFilterApplied && <View style={styles.tealDot} />}
                                    </View>
                                )}
                                {showRequestHistory && (
                                    <View style={{ alignItems: 'center' }}>
                                        <TouchableOpacity
                                            style={styles.createRequestIconStyle}
                                            onPress={handleRequestHistory}>
                                            <HistoryClockIcon height={20} width={20} />
                                        </TouchableOpacity>
                                        {historyUnreadMessageStatus && <View style={styles.unreadMessageStatus} />}
                                    </View>
                                )}
                                {showCreateRequestIcon && (
                                    <TouchableOpacity onPress={handleCreateRequest}>
                                        <RequestUpdatedIcon height={28} width={28} />
                                    </TouchableOpacity>
                                )}
                                {showCreateChatIcon && (
                                    <TouchableOpacity onPress={handleCreateChat} style={{ marginRight: 15 }}>
                                        <CreateChatIcon height={Size.SIZE_20} width={Size.SIZE_20} />
                                    </TouchableOpacity>
                                )}
                                {showTripleDot && (
                                    <TouchableOpacity onPress={handleToggleRecentPopup}>
                                        <WhiteTripleDot height={Size.SIZE_20} width={Size.SIZE_5} />
                                    </TouchableOpacity>
                                )}
                            </View>
                        )}
                    </View>
                </View>
            </ImageBackground>
        </>
    );
};

export default HomeScreenCommonHeader;

const styles = StyleSheet.create({
    container: {
        width: '100%',
        zIndex: 100,
        // backgroundColor: 'white',
        // borderBottomColor: colors.headerBorderColor,
    },
    profileIcon: {
        width: Size.SIZE_34,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.whiteRGB,
        alignItems: 'center',
        justifyContent: 'center',
    },
    welcomeBackStyle: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.greyRgb,
        lineHeight: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
    },
    text: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.whiteRGB,
        lineHeight: Typography.FONT_SIZE_18,
        fontFamily: 'Ubuntu-Medium',
    },
    createRequestIconStyle: {
        backgroundColor: colors.tealRgb,
        height: Size.SIZE_26,
        width: Size.SIZE_26,
        borderRadius: Size.SIZE_8,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        marginRight: Spacing.SCALE_18,
    },
    unreadMessageStatus: {
        width: Size.SIZE_6,
        height: Size.SIZE_6,
        backgroundColor: colors.orange,
        borderRadius: Size.SIZE_50,
        position: 'absolute',
        right: -2,
        top: -2,
        marginRight: Spacing.SCALE_18,
    },
    mapToggle: {
        borderRadius: Size.SIZE_8,
        padding: Spacing.SCALE_4,
        backgroundColor: colors.whiteRGB,
        marginRight: Spacing.SCALE_15,
    },
    mapToggleIconTeal: {
        height: Size.SIZE_20,
        width: Size.SIZE_20,
        backgroundColor: colors.tealRgb,
        borderRadius: Size.SIZE_6,
        alignItems: 'center',
        justifyContent: 'center',
    },
    mapToggleIcon: {
        height: Size.SIZE_20,
        width: Size.SIZE_20,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_6,
        alignItems: 'center',
        justifyContent: 'center',
    },
    mapToggleIconContainer: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_4,
    },
    filterIcon: {
        borderWidth: 1.3,
        borderColor: colors.whiteRGB,
        borderRadius: Size.SIZE_8,
        padding: Spacing.SCALE_4,
    },
    tealDot: {
        width: Size.SIZE_6,
        height: Size.SIZE_6,
        backgroundColor: colors.tealRgb,
        borderRadius: Size.SIZE_50,
        position: 'absolute',
        right: -2,
        top: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
    },
});

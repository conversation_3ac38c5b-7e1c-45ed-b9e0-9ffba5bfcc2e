// src/components/layout/ScreenWrapper.tsx

import React from 'react';
import {
    View,
    StyleSheet,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleProp,
    ViewStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors } from '../../theme/theme'; // Adjust this path if your theme file is located elsewhere

// Define the type for the component's props
interface ScreenWrapperProps {
    /** The content to be rendered inside the wrapper. */
    children: React.ReactNode;
    /** Any additional styles to apply to the main container. */
    style?: StyleProp<ViewStyle>;
    /** Set to true to wrap the content in a ScrollView for scrollable screens. */
    withScrollView?: boolean;
    /** 
     * Defaults to true. Set to false if you don't need bottom padding. 
     * Useful for screens with a KeyboardAvoidingView input at the bottom (like a chat screen).
     */
    withBottomInset?: boolean;
    /** 
     * Defaults to true. Set to false for screens with custom transparent headers 
     * where content should go right to the top edge.
     */
    withTopInset?: boolean;
}

/**
 * A reusable, type-safe screen wrapper that automatically handles safe area insets
 * to avoid system UI elements like the status bar and bottom gesture bar.
 * It also integrates KeyboardAvoidingView for screens with text inputs.
 */
const ScreenWrapper: React.FC<ScreenWrapperProps> = ({
    children,
    style,
    withScrollView = false,
    withBottomInset = true,
    withTopInset = true,
}) => {
    const insets = useSafeAreaInsets();

    // Combine the base style, custom styles, and conditional padding into one array
    const containerStyle: StyleProp<ViewStyle> = [
        styles.container,
        style,
        // Conditionally apply padding based on the props. This makes the component highly flexible.
        withTopInset && { paddingTop: insets.top },
        withBottomInset && { paddingBottom: insets.bottom < 40 ? 0 : insets.bottom },
    ];

    // Decide whether the main content container is a regular View or a ScrollView
    const ContentContainer = withScrollView ? ScrollView : View;

    return (
        // KeyboardAvoidingView is included to ensure text inputs aren't hidden by the keyboard.
        <ContentContainer style={containerStyle}>{children}</ContentContainer>
    );
};

const styles = StyleSheet.create({
    flex: {
        flex: 1,
    },
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB, // Set your app's default background color from your theme
    },
});

export default ScreenWrapper;
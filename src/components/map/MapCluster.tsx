import React, { memo } from 'react';
import { SymbolLayer, ShapeSource } from '@rnmapbox/maps';
import { OnPressEvent } from '@rnmapbox/maps/lib/typescript/src/types/OnPressEvent';

//Assets, Interfaces, Constants, Utils and Theme
import {
    BLUE,
    BLUE_CONTACT,
    GRE<PERSON>,
    GREEN_CONTACT,
    GREY,
    GREY_CONTACT,
    TEAL,
    TEAL_CONTACT,
    YELLOW,
    YELLOW_CONTACT,
} from '../../screens/my-TG-Stream-Chat/client';
import { colors } from '../../theme/theme';
import { MapClub } from '../../interface';

const MapCluster = ({ clubs, onItemPress }: { clubs: MapClub[]; onItemPress: (event: OnPressEvent) => void }) => {
    return (
        <ShapeSource
            onPress={onItemPress}
            id="clubMarkersSource"
            shape={{
                type: 'FeatureCollection',
                //@ts-ignore
                features: clubs || [],
            }}
            cluster={true}
            clusterMaxZoomLevel={5}
            clusterRadius={40}
            tolerance={0.45}>
            <SymbolLayer
                id="tealClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', TEAL]]}
                style={{
                    iconImage: 'teal-club',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />
            <SymbolLayer
                id="tealContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', TEAL_CONTACT]]}
                style={{
                    iconImage: 'teal-contact',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="greenClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREEN]]}
                style={{
                    iconImage: 'green-club',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="greenContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREEN_CONTACT]]}
                style={{
                    iconImage: 'green-contact',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="blueClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', BLUE]]}
                style={{
                    iconImage: 'blue-club',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="blueContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', BLUE_CONTACT]]}
                style={{
                    iconImage: 'blue-contact',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="greyClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREY]]}
                style={{
                    iconImage: 'grey-club',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="greyContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', GREY_CONTACT]]}
                style={{
                    iconImage: 'grey-contact',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="yellowClub"
                filter={['all', ['!has', 'point_count'], ['==', 'color', YELLOW]]}
                style={{
                    iconImage: 'yellow-club',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="yellowContact"
                filter={['all', ['!has', 'point_count'], ['==', 'color', YELLOW_CONTACT]]}
                style={{
                    iconImage: 'yellow-contact',
                    iconSize: ['case', ['get', 'isSelected'], 0.6, 0.5],
                    iconAllowOverlap: true,
                    textField: ['step', ['zoom'], '', 11, ['get', 'name']],
                    textSize: 14,
                    textOffset: [0, 1.5],
                    textAnchor: 'top',
                    textColor: colors.tealRgb,
                }}
            />

            <SymbolLayer
                id="cluster-count"
                // @ts-ignore
                type="symbol"
                filter={['all', ['has', 'point_count']]}
                style={{
                    iconImage: 'cluster-marker',
                    iconSize: 0.4,
                    textField: '{point_count}',
                    textSize: 12,
                    iconAllowOverlap: true,
                    iconOffset: [0, -5],
                }}
            />
        </ShapeSource>
    );
};

export default memo(MapCluster);
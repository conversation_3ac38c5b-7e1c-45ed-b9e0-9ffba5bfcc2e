import { useState, useEffect, useContext, useMemo } from 'react';

import { MapClub, User, MapClubDetail } from '../interface';
import { autocomplete, clubSearchV2, fetchclubsNew } from '../service/EndPoint';
import { fetcher } from '../service/fetcher';
import { GlobalContext } from '../context/contextApi';
import { BLUE, BLUE_CONTACT } from '../screens/my-TG-Stream-Chat/client';
import { ALL, CLUB_PERCENTAGE, MORE_THAN_10_MEMBERS, PLAY_AS_A_COUPLE } from '../utils/constants/strings';
import { FilterState } from '../interface';

// Active View
// - Map - clubs in viewport
// - List - all clubs
// Active Tab
// - All clubs
// - My Clubs
// - Favorite Clubs
// Filter
// - All applied filters
// Search
// - clubs from search

/**
 * @typedef {Object} Filter
 * @property {boolean} fern
 * @property {boolean} sage
 * @property {boolean} moss
 * @property {boolean} olive
 * @property {boolean} friendsAndContact
 * @property {boolean} myTGGroupMember
 * @property {Array} selectedTGGroup
 * @property {boolean} openOfferClubs
 * @property {boolean} favoriteClubs
 * @property {boolean} playedClubs
 * @property {boolean} playAsCouple
 * @property {boolean} clubswithFemaleMembers
 * @property {any} category
 */

const useMapState = ({ user }: { user: User }) => {
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // general state
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    const [mapClubs, setMapClubs] = useState<MapClub[]>([]); // all clubs
    const [activeView, setActiveView] = useState('MAP'); // 'MAP', 'LIST'
    const [activeQueryType, setActiveQueryType] = useState('NONE'); // 'NONE', 'FILTER', 'DYNAMIC SEARCH'
    const [activeTab, setActiveTab] = useState('ALL'); // 'ALL', 'FAVORITED', 'MY CLUBS'
    const [searchTimeout, setSearchTimeout] = useState();
    const [moreDetailListView, setMoreDetailListView] = useState(false);
    const [isComeFromMap, setIsComeFromMap] = useState(false);
    const [filter, setFilter] = useState<FilterState>({
        fern: false,
        sage: false,
        moss: false,
        olive: false,
        friendsAndContact: false,
        myTGGroupMember: false,
        selectedTGGroup: [],
        openOfferClubs: false,
        favoriteClubs: false,
        playedClubs: false,
        playAsCouple: false,
        clubswithFemaleMembers: false,
        category: '',
        clubPercentage: ALL,
        clubMemberCount: ALL,
    });

    const [searchTerm, setSearchTerm] = useState<string>('');
    const [searching, setSearching] = useState(false);
    //this state is used to set boundries when we change the location on map;
    const [clubsInViewport, setClubsInViewport] = useState([]); // tracks which clubs are currently within the mapBoundaries, for use in list view
    const [clubDetailDrawerOpen, setClubDetailDrawerOpen] = useState(false);
    const [selectedClub, setSelectedClub] = useState<MapClubDetail | null>(null); // the club that is within the club detail drawer
    const [searchedClubList, setSearchedClubList] = useState<MapClub[]>([]);
    const [locations, setLocations] = useState([]);
    const [zoomLevel, setZoomLevel] = useState<number>(0);
    const [tierCount, setTierCount] = useState({
        Fern: 0,
        Moss: 0,
        Sage: 0,
        Olive: 0,
    });
    const [playedCount, setPlayedCount] = useState(0);
    const [femaleCount, setFemaleCount] = useState(0);
    const [playAsCoupleCount, setPlayAsCoupleCount] = useState(0);

    const { state, actions } = useContext(GlobalContext);

    //allClubState is a state of all clubs
    const { allClubsState, allClubsData, mapBoundaries, filterActive } = useMemo(() => {
        const { allClubsState, allClubsData, mapBoundaries, filterActive } = state;
        return { allClubsState, allClubsData, mapBoundaries, filterActive };
    }, [state?.allClubsState, state?.allClubsData, state?.mapBoundaries, state?.filterActive]);

    useEffect(() => {
        getMapClubs();
    }, [user]);

    useEffect(() => {
        if (mapBoundaries) {
            handleBoundriesChange();
        }
    }, [mapBoundaries, allClubsState, filter, mapClubs]);

    useEffect(() => {
        if (state?.mapFilterState) {
            setFilter(state?.mapFilterState);
        }
    }, [state?.mapFilterState, allClubsData]);

    const handleBoundriesChange = () => {
        // if(filterActive) return;
        let boundariesClub: MapClub[] = [];
        mapClubs?.map((club: MapClub) => {
            if (
                club.geometry?.coordinates &&
                mapBoundaries?.[0] &&
                mapBoundaries?.[1] &&
                club.geometry.coordinates[1] <= mapBoundaries[0][1] &&
                club.geometry.coordinates[1] >= mapBoundaries[1][1] &&
                club.geometry.coordinates[0] <= mapBoundaries[0][0] &&
                club.geometry.coordinates[0] >= mapBoundaries[1][0]
            ) {
                boundariesClub.push(club);
            }
        });
        actions?.setBoundariesChangeClubs(boundariesClub);
    };
    //Close drawer if not selected club
    useEffect(() => {
        if (activeQueryType !== 'NONE') {
            /*
      If dynamic search or filter are selected, close the club detail drawer
       */
            setClubDetailDrawerOpen(false);
            setSelectedClub(null);
        }
    }, [activeQueryType]);

    useEffect(() => {
        if (!searchTerm || searchTerm?.length <= 0) {
            setSearching(false);
        } else setSearching(true);

        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        setSearchTimeout(
            //@ts-ignore
            setTimeout(() => {
                if (searchTerm) {
                    getLocations();
                    handleSearchClub();
                }
            }, 500),
        );
        return () => clearTimeout(searchTimeout);
    }, [searchTerm]);

    const handleSearchClub = () => {
        const tempClubs: MapClub[] = mapClubs?.filter((club: MapClub) =>
            club.properties.name.toLowerCase().includes(searchTerm?.trim()?.toLowerCase()),
        );
        setSearchedClubList(tempClubs);
    };

    const getLocations = () => {
        fetch(`${autocomplete}${searchTerm}`)
            .then((data) =>
                data.json().then(({ predictions }) => {
                    if (searching && searchTerm.length) {
                        if (predictions && predictions.length > 0) {
                            setLocations(predictions);
                        }
                    } else setLocations([]);
                }),
            )
            .catch((err) => {});
    };

    //This function is used to filter the value
    const handleMapFilter = () => {
        if (filterActive) {
            let tempClubs = [],
                tgFilterFriendsClub = [],
                tgFilterGroupMemberClub = [];
            //Filter clubs on the basis of tier
            const visibleTiers = getVisibleTiers();
            tempClubs = allClubsState?.filter((club: MapClub) => visibleTiers?.includes(club?.properties?.tier));

            //Filter clubs on the basis of TG Community
            if (filter?.friendsAndContact) {
                tgFilterFriendsClub = tempClubs?.filter((club: MapClub) => {
                    return club?.properties?.isFriend || club?.properties?.isContact;
                });
            }

            console.log("filter?.myTGGroupMember", filter?.myTGGroupMember)
            console.log("filter?.selectedTGGroup?.length", filter?.selectedTGGroup?.length, filter)
            if (filter?.myTGGroupMember) {
                if (!filter?.selectedTGGroup?.length) {
                    tgFilterGroupMemberClub = tempClubs?.filter((club: MapClub) => {
                        return club?.properties?.isMyTgGroupMember;
                    });
                } else {
                    let selectedGroupId = filter?.selectedTGGroup.map((item: any) => item.id);
                    console.log("selectedGroupId", selectedGroupId)
                    tgFilterGroupMemberClub = tempClubs?.filter((club: MapClub) => {
                        return !!club?.properties?.myTgGroupIds?.filter((e: any) => selectedGroupId.indexOf(e) !== -1)
                            ?.length;
                    });
                    console.log("tgFilterGroupMemberClub", tgFilterGroupMemberClub)
                }
            }

            if (filter?.friendsAndContact || filter?.myTGGroupMember) {
                tempClubs = [...tgFilterFriendsClub, ...tgFilterGroupMemberClub];
            }

            if (
                filter?.openOfferClubs ||
                filter?.favoriteClubs ||
                filter?.playedClubs ||
                filter?.playAsCouple ||
                filter?.clubswithFemaleMembers
            ) {
                //User fav clubs
                const userFavClubs = user.favorite_clubs.map((club) => club.club_id);

                //Other filters
                tempClubs = tempClubs?.filter((club: MapClub) => {
                    //Club has open offers
                    if (
                        filter?.openOfferClubs &&
                        (club?.properties?.color === BLUE || club?.properties?.color === BLUE_CONTACT)
                    ) {
                        return true;
                    }

                    //Favorite clubs
                    if (filter?.favoriteClubs && userFavClubs.includes(club.id)) {
                        return true;
                    }

                    //Played clubs
                    if (filter?.playedClubs && user.playedClubs.includes(club.id)) {
                        return true;
                    }

                    //Play as Couple
                    if (filter?.playAsCouple && club?.properties?.hasPlayAsCoupleMember) {
                        return true;
                    }

                    //Clubs with Female Members
                    if (filter?.clubswithFemaleMembers && club?.properties.hasFemalMember) {
                        return true;
                    }

                    return false;
                });
            }

            //Filter clubs on the basis of club member count
            if (filter?.clubMemberCount === MORE_THAN_10_MEMBERS) {
                tempClubs = tempClubs?.filter((club: MapClub) => club?.properties?.clubMembers > 10);
            }

            //Filter clubs on the basis of club percentage
            if (filter?.clubPercentage === CLUB_PERCENTAGE) {
                tempClubs = tempClubs?.filter((club: MapClub) => club?.properties?.acceptanceRate > 25);
            }
            setMapClubs(tempClubs);
        } else {
            setMapClubs(allClubsState);
        }
    };

    useEffect(() => {
        handleMapFilter();
    }, [filter, allClubsData]);

    const getMapClubs = () => {
        actions?.setAppLoader(true);
        setPlayedCount(0);
        setFemaleCount(0);
        setPlayAsCoupleCount(0);
        setTierCount({
            Fern: 0,
            Moss: 0,
            Sage: 0,
            Olive: 0,
        });
        const body = {
            userId: user?.id,
        };
        fetcher({
            endpoint: fetchclubsNew,
            method: 'POST',
            body,
        })
            .then((data) => {
                setClubsInViewport(data?.clubs);
                //set all clubs
                actions?.setAllClubs(data?.clubs);
                //data of all clubs
                actions?.setAllClubsData(data?.clubs);
                getCounts(data?.clubs);
                actions?.setAppLoader(false);
            })
            .catch((err) => {
                actions?.setAppLoader(false);
            });
    };

    const getCounts = (clubs: MapClub[]) => {
        clubs.forEach((club) => {
            if (club.properties.played) {
                setPlayedCount((prev) => prev + 1);
            }
            if (club.properties.tier === 0 || club.properties.tier === 1) {
                setTierCount((prev) => ({ ...prev, Fern: prev.Fern + 1 }));
            }
            if (club.properties.tier === 2) {
                setTierCount((prev) => ({ ...prev, Sage: prev.Sage + 1 }));
            }
            if (club.properties.tier === 3) {
                setTierCount((prev) => ({ ...prev, Moss: prev.Moss + 1 }));
            }
            if (club.properties.tier === 5) {
                setTierCount((prev) => ({ ...prev, Olive: prev.Olive + 1 }));
            }
            if (club.properties.hasFemalMember) {
                setFemaleCount((prev) => prev + 1);
            }
            if (club.properties.hasPlayAsCoupleMember) {
                setPlayAsCoupleCount((prev) => prev + 1);
            }
        });
    };

    function getVisibleTiers() {
        let tiers = [];
        if (!filter.fern && !filter.sage && !filter.moss && !filter.olive) {
            tiers.push(0);
            tiers.push(1);
            tiers.push(2);
            tiers.push(3);
            tiers.push(5);
        } else {
            if (filter.fern) {
                tiers.push(0);
                tiers.push(1);
            }
            if (filter.sage) tiers.push(2);
            if (filter.moss) tiers.push(3);
            if (filter.olive) tiers.push(5);
        }
        return tiers;
    }

    return {
        // general state
        clubs: mapClubs,
        setMapClubs,
        activeQueryType,
        setActiveQueryType,
        activeView,
        setActiveView,
        activeTab,
        setActiveTab,
        // activeView: MAP
        clubDetailDrawerOpen,
        selectedClub,
        mapBoundaries,
        setClubDetailDrawerOpen,
        setSelectedClub,
        // activeView: LIST
        searchedClubList,
        // activeQueryType: FILTER
        filter,
        setFilter,
        filterActive,
        setFilterActive: actions?.setFilterActive,
        // activeQueryType: DYNAMIC SEARCH
        searchTerm,
        setSearchTerm,
        locations,
        searchResultsLoading: false,
        searching,
        setSearching,
        moreDetailListView,
        setMoreDetailListView,
        isComeFromMap,
        setIsComeFromMap,
        zoomLevel,
        setZoomLevel,
        handleBoundriesChange,
        tierCount,
        playedCount,
        femaleCount,
        playAsCoupleCount,
    };
};

export default useMapState;

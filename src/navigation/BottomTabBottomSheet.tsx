import React, { useRef, useEffect, useContext, memo, useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, Platform } from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { useNavigation, useIsFocused } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
const CleverTap = require('clevertap-react-native');

import {
    NewFriendsIcon,
    NewPegboardIcon,
    NewHelpIcon,
    NewOffersIcon,
    NewEventsIcon,
    NewBenefitsIcon,
    BottomDrawerIcon,
    NewGroupIcon,
    NewFeedIcon,
} from '../assets/svg/index';
import { Size, Spacing, Typography } from '../utils/responsiveUI';
import { colors } from '../theme/theme';
import config from '../config';
import { FeatureProps } from '../interface';
import constants, { tiers } from '../utils/constants/constants';
import { AuthContext } from '../context/AuthContext';
import { GlobalContext } from '../context/contextApi';
import { fetcher } from '../service/fetcher';
import { UPDATE_TEAL_DOT_STATUS } from '../service/EndPoint';
import { handleStreamNotificationPress } from '../components/layout/notifications/streamNotificationHandling';
import { StreamChatContext } from '../context/StreamChatContext';
import { THOUSAND_GREENS_PUBLIC_ID } from '../screens/my-TG-Stream-Chat/client';
import { apiServices } from '../service/apiServices';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const BottomSheetComponent = () => {
    const insets = useSafeAreaInsets();
    const navigation = useNavigation<NativeStackNavigationProp<any>>();
    const { chatClient, setChannel } = useContext(StreamChatContext);

    const isFocused = useIsFocused();
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const snapPoints = Platform.OS === 'ios' ? ['3%', '29%'] : ['3%', '29%'];
    const bottomSheetRef = useRef<BottomSheet>(null);
    const [features, setFeatures] = useState<FeatureProps[]>([
        {
            name: 'Groups',
            //@ts-ignore
            Icon: NewGroupIcon,
            navigateTo: config.routes.GROUPS,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_GROUPS,
            redDotName: 'group',
        },
        {
            name: 'Friends',
            //@ts-ignore
            Icon: NewFriendsIcon,
            navigateTo: config.routes.MY_TG_FRIENDS_SCREEN,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_FRIENDS,
            redDotName: 'friend',
        },
        {
            name: 'Pegboard',
            //@ts-ignore
            Icon: NewPegboardIcon,
            navigateTo: config.routes.PEGBOARD,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_PEGBOARD,
            redDotName: 'pegboard',
        },
        {
            name: 'Help',
            //@ts-ignore
            Icon: NewHelpIcon,
            navigateTo: config.routes.FAQ,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_HELP,
            redDotName: 'help',
        },
        {
            name: 'Offers',
            //@ts-ignore
            Icon: NewOffersIcon,
            navigateTo: config.routes.OFFER_SCREEN,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_OFFERS,
            redDotName: 'offer',
        },
        {
            name: 'Events',
            //@ts-ignore
            Icon: NewEventsIcon,
            navigateTo: config.routes.EVENTS,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_EVENTS,
            redDotName: 'event',
        },
        {
            name: 'Benefits',
            //@ts-ignore
            Icon: NewBenefitsIcon,
            navigateTo: config.routes.BENEFITS,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_BENEFITS,
            redDotName: 'benefit',
        },
        {
            name: 'Feed',
            //@ts-ignore
            Icon: NewFeedIcon,
            navigateTo: config.routes.FEED,
            isRedDot: false,
            eventName: constants.CLEVERTAP.CLICK_FEED,
            redDotName: 'feed',
        },
    ]);

    useEffect(() => {
        if (state.tealDotStatus) {
            setFeatures(
                features.map((feature) => {
                    return { ...feature, isRedDot: state.tealDotStatus?.tealDotStatus[feature?.redDotName || ''] };
                }),
            );
        }
    }, [state.tealDotStatus]);

    useEffect(() => {
        if (state.isFirstTimeVisitHome) {
            actions.setMenuBottomSheetOpenIndex(1);
            actions.setIsFirstTimeVisitHome(false);
        }
    }, []);

    const handleTealDotStatus = useCallback(async () => {
        if (user?.id) {
            let res = await apiServices.getTealDotStatus({ userId: user?.id });
            actions.setTealDotStatus(res?.data);
        }
    }, [user?.id]);

    useEffect(() => {
        const interval = setInterval(() => {
            handleTealDotStatus();
        }, 100000);

        return () => clearInterval(interval);
    }, [handleTealDotStatus]); // Add dependency to avoid stale closure

    const handleMarkRead = async (flagKey: string) => {
        if (!flagKey) return;
        let res = await fetcher({
            endpoint: UPDATE_TEAL_DOT_STATUS,
            method: 'POST',
            body: {
                userId: user?.id,
                flagKey,
                value: false,
            },
        });
        if (res?.status) {
            handleTealDotStatus();
        }
    };

    const handleGridCardPress = (navigateTo: string, eventName: string, redDotName: string, isRedDot: boolean) => {
        if (navigateTo === config.routes.FEED) {
            actions.setAppLoader(true);
            handleStreamNotificationPress(chatClient, user, THOUSAND_GREENS_PUBLIC_ID, navigation, setChannel, actions);
            return;
        }
        CleverTap.recordEvent(eventName, {
            'User email': user?.email,
            'Current Account Status': user?.muted ? 'Muted' : 'Unmuted',
            Membership: user?.membership_plan?.name,
            Tier: tiers[user?.tier],
        });
        if (isRedDot) {
            handleMarkRead(redDotName);
        }
        navigation.navigate(navigateTo);
    };

    const FeatureButton: React.FC<FeatureProps> = ({ name, Icon, navigateTo, eventName, isRedDot, redDotName }) => (
        <TouchableOpacity
            style={[styles.button]}
            onPress={() => handleGridCardPress(navigateTo, eventName, redDotName || '', isRedDot)}
            activeOpacity={0.7}>
            {isRedDot && <View style={styles.tealDot} />}
            <Icon width={Size.SIZE_46} height={Size.SIZE_34} />
            <Text style={styles.label}>{name}</Text>
        </TouchableOpacity>
    );

    const GridCard = () => {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    justifyContent: 'center',
                    borderTopLeftRadius: Size.SIZE_16,
                    borderTopRightRadius: Size.SIZE_16,
                    backgroundColor: 'transparent',
                }}>
                {features.map((feature, index) => (
                    <FeatureButton key={index} {...feature} />
                ))}
            </View>
        );
    };

    const handleSheetChanges = useCallback((index: number) => {
        actions.setIsMenuBottomSheetOpen(index > 0);
        actions.setMenuBottomSheetOpenIndex(index);
    }, []);

    return (
        <BottomSheet
            ref={bottomSheetRef}
            snapPoints={snapPoints}
            index={state.isFirstTimeVisitHome ? 1 : state.isMenuBottomSheetOpen ? 1 : 0}
            enableOverDrag={false}
            bottomInset={Size.SIZE_80 + (insets.bottom < 40 ? 0 : insets.bottom)}
            backgroundStyle={{ backgroundColor: 'transparent' }}
            onChange={handleSheetChanges}
            handleComponent={() => (
                <View
                    style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        pointerEvents: 'none',
                        borderRadius: Size.SIZE_16,
                        backgroundColor: 'transparent',
                    }}>
                    <BottomDrawerIcon height={Size.SIZE_8} width={Size.SIZE_50} />
                </View>
            )}
            style={[
                Platform.select({
                    ios: {
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: -4,
                        },
                        shadowOpacity: 0.1,
                        shadowRadius: 4,
                    },
                    android: {
                        elevation: 25,
                        shadowColor: '#000',
                        shadowRadius: 4,
                        borderColor: 'transparent',
                        borderRadius: 15,
                    },
                }),
            ]}>
            <BottomSheetView style={[styles.contentContainer, { paddingBottom: insets.bottom < 40 ? 0 : insets.bottom + Size.SIZE_6, }]}>
                <GridCard />
            </BottomSheetView>
        </BottomSheet>
    );
};

export default memo(BottomSheetComponent);

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    contentContainer: {
        flex: 1,
        borderTopLeftRadius: Size.SIZE_16,
        borderTopRightRadius: Size.SIZE_16,
        backgroundColor: colors.whiteRGB,
    },
    button: {
        width: Dimensions.get('window').width * 0.16,
        height: Size.SIZE_58,
        borderRadius: Size.SIZE_20,
        alignItems: 'center',
        justifyContent: 'center',
        margin: Spacing.SCALE_16,
        position: 'relative',
    },
    label: {
        marginTop: Spacing.SCALE_4,
        fontSize: Typography.FONT_SIZE_11,
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
    },
    tealDot: {
        position: 'absolute',
        top: Spacing.SCALE_3,
        right: Spacing.SCALE_10,
        backgroundColor: colors.tealRgb,
        height: Size.SIZE_6,
        width: Size.SIZE_6,
        borderRadius: Size.SIZE_50,
        zIndex: 1,
    },
});

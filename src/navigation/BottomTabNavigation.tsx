import React, { use<PERSON>allback, useContext, useEffect, useState } from 'react';
import { View, StyleSheet, Platform, Keyboard } from 'react-native';
import { BottomTabScreenProps, createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ParamListBase } from '@react-navigation/native';

import { RootStackParamList } from '../interface/type';

const CleverTap = require('clevertap-react-native');

import HomeScreen from '../screens/HomeScreen/view/HomeScreen';
import BottomSheetComponent from './BottomTabBottomSheet';
import {
    ChatIconNew,
    HomeIcon,
    GolfIconNew,
    RequestIconNew,
    NewHomeIcon,
    NewClubsIcon,
    NewRequestsIcon,
    NewTGChatIcon,
} from '../assets/svg';
import TabBarLabel from './TabBarLabel';
import TabBarIcon from './TabBarIcon';
import ClubsScreen from '../screens/map/ClubsScreen';
import TGChat from '../screens/my-TG-Stream-Chat/view/TGChat';
import { colors } from '../theme/theme';
import { Size, Spacing } from '../utils/responsiveUI';
import GuideScreen from '../screens/HomeScreen/view/GuideScreen';
import { GlobalContext } from '../context/contextApi';
import { StreamChatContext } from '../context/StreamChatContext';
import { AuthContext } from '../context/AuthContext';
import constants, { tiers } from '../utils/constants/constants';
import TGLoader from '../components/layout/TGLoader';
import config from '../config';
import { handleMarkRead } from '../utils/commonActions/handleMarkRead';
import { REQUEST_CHAT_GROUP } from '../screens/my-TG-Stream-Chat/client';
import { apiServices } from '../service/apiServices';
import RequestScreenWrapper from './RequestScreenWrapper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type TabParamList = ParamListBase & {
    Home: undefined;
    Clubs: undefined;
    RequestScreen: undefined;
    TGChat: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();

const BottomTabNavigation = () => {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const { unreadcount, client } = useContext(StreamChatContext);
    const [isKeyboardVisible, setKeyboardVisible] = useState(false);
    // This hook provides the insets for all sides (top, bottom, left, right)
    const insets = useSafeAreaInsets();

    useEffect(() => {
        const messageNewEventListener = client.on('message.new', async (event: any) => {
            getStreamRequestChannel();
        });

        return () => {
            messageNewEventListener?.unsubscribe();
        };
    }, []);

    useEffect(() => {
        if (client?.user) {
            getStreamRequestChannel();
        }
    }, [client?.user]);

    //Get stream unread messages
    const getStreamRequestChannel = async () => {
        try {
            let page = 0;
            let updatedUnreadChannelsIds: any[] = [];
            let updatedChannels: any[] = [];
            while (1) {
                const channels = await client?.queryChannels(
                    {
                        type: {
                            $in: [REQUEST_CHAT_GROUP],
                        },
                        members: { $in: [user?.id] },
                        has_unread: true,
                    },
                    {},
                    { limit: 30, offset: 30 * page },
                );
                page = page + 1;
                if (!channels?.length) {
                    break;
                } else {
                    let requestIds = channels.map((data: any) => data?.data?.request_id);
                    const unreadChannelsIds = Array.from(new Set([...requestIds]));
                    updatedUnreadChannelsIds = [...updatedUnreadChannelsIds, ...unreadChannelsIds];
                    updatedChannels = [...updatedChannels, ...channels];
                }
            }
            actions.setUnreadChannels(updatedUnreadChannelsIds);
            actions.setUnreadChannelsObject(updatedChannels);
        } catch (error) {
            console.log('error==>>', error);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            // Do something when the screen is focused

            return () => {
                actions.setIsMenuBottomSheetOpen(false);
                actions.setMenuBottomSheetOpenIndex(0);
            };
        }, []),
    );

    useEffect(() => {
        const keyboardWillShowListener = Keyboard.addListener(
            Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
            () => {
                setKeyboardVisible(true);
            },
        );
        const keyboardWillHideListener = Keyboard.addListener(
            Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
            () => {
                setKeyboardVisible(false);
            },
        );

        return () => {
            keyboardWillShowListener.remove();
            keyboardWillHideListener.remove();
        };
    }, []);

    // Couched marker state
    useEffect(() => {
        actions.setStartGuide(user?.is_tutorial_viewed ? false : true);
    }, []);

    const handleTealDotStatus = useCallback(async () => {
        if (user?.id) {
            let res = await apiServices.getTealDotStatus({ userId: user?.id });
            actions.setTealDotStatus(res?.data);
        }
    }, [user?.id]);

    const handleTabPress = (tabName: string) => {
        handleTealDotStatus();
        let eventName = '';
        switch (tabName) {
            case 'Home':
                eventName = constants.CLEVERTAP.CLICK_HOME;
                break;
            case 'Clubs':
                eventName = constants.CLEVERTAP.CLICK_CLUBS;
                break;
            case 'Requests':
                eventName = constants.CLEVERTAP.CLICK_REQUESTS;
                break;
            case 'TGChat':
                eventName = constants.CLEVERTAP.CLICK_CHAT;
                break;
        }

        if (eventName) {
            CleverTap.recordEvent(eventName, {
                'User email': user?.email,
                'Current Account Status': user?.muted ? 'Muted' : 'Unmuted',
                Membership: user?.membership_plan?.name,
                Tier: tiers[user?.tier],
            });
        }
        actions.setIsMenuBottomSheetOpen(false);
        actions.setMenuBottomSheetOpenIndex(0);
    };

    // Determine if the bottom sheet should be visible with clear, simple logic
    const shouldShowBottomSheet = () => {
        // Rule 1: Always hide if the keyboard is visible. This is the highest priority.
        if (isKeyboardVisible) {
            return false;
        }

        // Rule 2: If on the Map screen, hide it if the map is expanded or search is active.
        if (state.screen === 'Map' && (state.isMapViewExpanded || state.isMapSearchActive)) {
            return false;
        }

        // If none of the above rules apply, show the component.
        return true;
    };

    return (
        <>
            <Tab.Navigator
                screenOptions={({ route }) => ({
                    tabBarHideOnKeyboard: Platform.OS === 'android',
                    tabBarIcon: ({ focused }) => {
                        // Determine the icon based on the route name
                        let icon;
                        switch (route.name) {
                            case 'Home':
                                icon = focused ? HomeIcon : NewHomeIcon;
                                break;
                            case 'Clubs':
                                icon = focused ? GolfIconNew : NewClubsIcon;
                                break;
                            case config.routes.REQUEST_SCREEN:
                                icon = focused ? RequestIconNew : NewRequestsIcon;
                                break;
                            case 'TGChat':
                                icon = focused ? ChatIconNew : NewTGChatIcon;
                                break;
                        }
                        return (
                            <View>
                                {route.name === 'TGChat' && unreadcount > 0 ? <View style={styles.tealDot} /> : null}
                                {route.name === 'RequestScreen' && state.unreadChannelsObject?.length ? (
                                    <View style={styles.tealDot} />
                                ) : null}
                                <TabBarIcon
                                    focused={focused}
                                    Icon={icon}
                                    backgroundColor={focused ? colors.activeTealColor : colors.lightgray}
                                />
                            </View>
                        );
                    },
                    tabBarLabel: ({ focused }) => {
                        // Determine the label based on the route name
                        let label;
                        switch (route.name) {
                            case 'Home':
                                label = 'Home';
                                break;
                            case 'Clubs':
                                label = 'Map';
                                break;
                            case config.routes.REQUEST_SCREEN:
                                label = 'Requests';
                                break;
                            case 'TGChat':
                                label = 'TG Chat';
                                break;
                        }
                        return <TabBarLabel focused={focused} label={label || ''} />;
                    },
                    tabBarStyle: {
                        height: Size.SIZE_80 + (insets.bottom < 40 ? 0 : insets.bottom),
                        backgroundColor: colors.whiteRGB,
                        borderTopWidth: 0,
                        elevation: 0,
                        // Add this line to apply the safe area inset as bottom padding
                        paddingBottom: insets.bottom < 40 ? 0 : insets.bottom,
                        ...((state.isMapViewExpanded || state.isMapSearchActive) && { display: 'none' }),
                    },
                    tabBarItemStyle: {
                        flex: 1, // Makes all tabs take equal width and height
                        height: Size.SIZE_80 + (insets.bottom < 40 ? 0 : insets.bottom),
                        marginTop: Spacing.SCALE_1,
                        
                    },

                    tabBarBackground: () => (
                        <View
                            style={{
                                backgroundColor: colors.whiteRGB,
                                flex: 1,
                                borderTopLeftRadius: Size.SIZE_12,
                                borderTopRightRadius: Size.SIZE_12,
                                paddingBottom: insets.bottom < 40 ? 0 : insets.bottom,
                            }}
                        />
                    ),
                })}>
                <Tab.Screen
                    name={'Home'}
                    component={HomeScreen}
                    options={{
                        headerShown: false,
                    }}
                    listeners={{
                        tabPress: () => {
                            handleTabPress('Home');
                            actions.setScreen('');
                        },
                    }}
                />
                <Tab.Screen
                    name="Clubs"
                    component={ClubsScreen}
                    options={{
                        headerShown: false,
                    }}
                    listeners={{
                        tabPress: () => {
                            handleTabPress('Clubs');
                            actions.setScreen('Map');
                        },
                    }}
                />
                <Tab.Screen
                    name="RequestScreen"
                    component={RequestScreenWrapper}
                    options={{
                        headerShown: false,
                    }}
                    listeners={{
                        tabPress: () => {
                            handleMarkRead('request', user?.id, actions);
                            handleTabPress('Requests');
                            actions.setScreen('');
                        },
                    }}
                />
                <Tab.Screen
                    name="TGChat"
                    component={TGChat}
                    options={{
                        headerShown: false,
                    }}
                    listeners={{
                        tabPress: () => {
                            handleTabPress('TGChat');
                            actions.setScreen('');
                        },
                    }}
                />
            </Tab.Navigator>
            {/* Full-Screen Transparent Overlay */}
            {state.startGuide && (
                <View style={styles.overlay}>
                    <GuideScreen />
                </View>
            )}
            {state.appLoader && <TGLoader size="large" color={colors.tealRgb} loading={state.appLoader} />}
            {/* Common BottomSheet */}
            {shouldShowBottomSheet() && <BottomSheetComponent />}
        </>
    );
};
export default BottomTabNavigation;

const styles = StyleSheet.create({
    overlay: {
        ...StyleSheet.absoluteFillObject, // Makes it cover the full screen
        backgroundColor: 'rgba(0,0,0,0.7)', // Semi-transparent background
        zIndex: 999, // Ensures it stays on top
    },
    tealDot: {
        height: Size.SIZE_6,
        width: Size.SIZE_6,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        top: Spacing.SCALE_4,
        left: Spacing.SCALE_36,
        borderRadius: Size.SIZE_50,
        zIndex: 1,
    },
    tabButton: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        borderTopLeftRadius: Size.SIZE_12,
        borderTopRightRadius: Size.SIZE_12,
    },
    activeTab: {
        backgroundColor: 'rgba(0, 128, 128, 0.05)', // Light teal background for active tab
    },
});

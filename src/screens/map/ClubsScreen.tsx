import React, { memo, useCallback, useContext, useEffect, useLayoutEffect, useState } from 'react';
import { BackHandler, View, Platform, KeyboardAvoidingView, PermissionsAndroid } from 'react-native';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';

//components, hooks and context imports
import { AuthContext } from '../../context/AuthContext';
import ClubMap from '../../components/map/ClubMap';
import useMapState from '../../hooks/useMapState';
import TGCustomModalView from '../../components/modals/TGCustomModalView';
import { GlobalContext } from '../../context/contextApi';
import ClubFilterFormNew from '../../forms/map-club/ClubFilterFormNew';
import HomeScreenCommonHeader from '../../components/homeScreenComponent/HomeScreenCommonHeader';
import TGSearchNew from '../my-TG-friends/view/TGSearch';

//utils, services, constants and assets imports
import { Spacing } from '../../utils/responsiveUI';
import { fetcher } from '../../service/fetcher';
import { CLUB_DETAILS_MAP } from '../../service/EndPoint';
import { MapClub } from '../../interface';
import { ALL, CLUB_MEMBER_COUNT_FILTER, CLUB_PERCENTAGE_FILTER } from '../../utils/constants/strings';

const ClubsScreen = ({ route, navigation }: { route: any; navigation: any }) => {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const [filterModalOpen, setFilterModalOpen] = useState<boolean>(false);
    const [isFilterApplied, setIsFilterApplied] = useState(false);
    const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);

    const {
        // general state
        clubs,
        setMapClubs,
        activeView,
        setActiveView,
        // activeView: MAP
        selectedClub,
        filter,
        mapBoundaries,
        setSelectedClub,
        setFilter,
        filterActive,
        setFilterActive,
        // activeQueryType: DYNAMIC SEARCH
        searchTerm,
        setSearchTerm,
        searchedClubList,
        locations,
        searching,
        setSearching,
        setMoreDetailListView,
        moreDetailListView,
        isComeFromMap,
        setIsComeFromMap,
        zoomLevel,
        setZoomLevel,
        tierCount,
        playedCount,
        femaleCount,
        playAsCoupleCount,
    } = useMapState({ user });

    useLayoutEffect(() => {
        console.log("state?.mapCurrentFilter", state?.mapCurrentFilter)
        if (state?.mapCurrentFilter) {
            const category = state?.mapCurrentFilter;
            setFilter({ ...filter, category });
            setFilterActive(true);
        }
        // Apply filter after come from friend or group screen and set filter value to take from global context state
        else if (route?.params?.category || state.mapFilterState || route?.params?.clubId) {
            if (route?.params?.clubId) {
                const club = clubs.find((club: MapClub) => club.id === route?.params?.clubId);
                const body = {
                    userId: user?.id,
                    clubId: Number(club?.id),
                    clubColor: club?.properties?.color,
                };
                fetcher({
                    endpoint: CLUB_DETAILS_MAP,
                    method: 'POST',
                    body,
                })
                    .then((data) => {
                        const { lat, lng } = data?.clubs;
                        if (lat && lng) {
                            setSelectedClub(data);
                        }
                    })
                    .catch((err) => {});
            }
            const category = state.mapFilterState.category;
            setFilter({ ...filter, category });
            setFilterActive(true);
            //@ts-ignore
            setZoomLevel(2);
        }
    }, [route?.params, state.mapFilterState]);

    //Handle hardware back button
    const backAction = () => {
        if (state?.isMapViewExpanded) {
            actions?.setIsMapViewExpanded(false);
        }
        return true;
    };

    const isFocused = useIsFocused();
    useEffect(() => {
        const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
        return () => backHandler.remove();
    }, [isFocused, state?.isMapViewExpanded]);

    useEffect(() => {
        for (let key in filter) {
            if (
                (filter as any)[key] === true ||
                filter[CLUB_MEMBER_COUNT_FILTER] !== ALL ||
                filter[CLUB_PERCENTAGE_FILTER] !== ALL
            ) {
                setIsFilterApplied(true);
                break;
            } else {
                setIsFilterApplied(false);
            }
        }
    }, [filter]);

    const checkAndroidPerms = () => {
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
            .then((permission: any) => {
                setLocationPermissionGranted(permission === 'denied' ? false : permission);
            })
            .catch((err) => {});
    };

    useFocusEffect(
        useCallback(() => {
            Platform.OS === 'android' && checkAndroidPerms();
        }, [clubs]),
    );

    return (
        <View style={[{ flex: 1 }, searchTerm && activeView == 'LIST' && { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
            <View style={{ flex: 1, backgroundColor: 'transparent' }}>
                {!state?.isMapViewExpanded && (Platform.OS === 'android' ? locationPermissionGranted : true) && (
                    <>
                        <HomeScreenCommonHeader
                            title="Map"
                            openFilterModal={() => setFilterModalOpen(true)}
                            showFilter
                            activeView={activeView}
                            setActiveView={setActiveView}
                            isFilterApplied={isFilterApplied}
                        />
                        {
                            <View
                                style={
                                    activeView === 'MAP'
                                        ? {
                                              position: 'absolute',
                                              top: Spacing.SCALE_100,
                                              zIndex: 1000,
                                              alignSelf: 'center',
                                          }
                                        : {}
                                }>
                                <TGSearchNew
                                    searchState={[searchTerm, setSearchTerm]}
                                    searchBoxWrapperStyle={{
                                        marginTop: Spacing.SCALE_5,
                                        marginBottom: Spacing.SCALE_6,
                                    }}
                                    placeholder="Search by Location or Club"
                                />
                            </View>
                        }
                    </>
                )}
                <ClubMap
                    clubs={clubs}
                    setMapClubs={setMapClubs}
                    activeView={activeView}
                    setActiveView={setActiveView}
                    searchedClubList={searchedClubList}
                    locations={locations}
                    searchingState={[searching, setSearching]}
                    searchInputState={[searchTerm, setSearchTerm]}
                    selectedClub={selectedClub}
                    setSelectedClub={setSelectedClub}
                    setMoreDetailListView={setMoreDetailListView}
                    moreDetailListView={moreDetailListView}
                    isComeFromMap={isComeFromMap}
                    setIsComeFromMap={setIsComeFromMap}
                    zoomLevel={zoomLevel}
                    setZoomLevel={setZoomLevel}
                    clubFromRoute={route?.params?.clubId}
                    filter={filter}
                />
                {filterModalOpen && (
                    <TGCustomModalView
                        isVisible={filterModalOpen}
                        style={{ flex: 1 }}
                        onPressOutSide={() => {}}
                        screen="Map">
                        <ClubFilterFormNew
                            setModal={() => setFilterModalOpen(false)}
                            filterState={[filter, setFilter]}
                            setFilterActive={setFilterActive}
                            tierCount={tierCount}
                            playedCount={playedCount}
                            femaleCount={femaleCount}
                            playAsCoupleCount={playAsCoupleCount}
                        />
                    </TGCustomModalView>
                )}
            </View>
        </View>
    );
}

export default memo(ClubsScreen);

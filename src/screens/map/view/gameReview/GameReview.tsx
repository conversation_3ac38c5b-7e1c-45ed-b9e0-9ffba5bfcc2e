import { StatusBar, StyleSheet, View, FlatList, RefreshControl } from 'react-native';
import React, { useContext, useLayoutEffect, useState } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';

// Components and context imports
import ProfileHeader from '../../../../components/layout/ProfileHeader';
import { AuthContext } from '../../../../context/AuthContext';
import GameReviewListingCard from './GameReviewListingCard';
import RequestScreenSkelton from '../../../requests/view/RequestScreenSkelton';
import { GlobalContext } from '../../../../context/contextApi';

// Theme, Interfaces and responsive UI imports
import { colors } from '../../../../theme/theme';
import { Spacing, Typography } from '../../../../utils/responsiveUI';
import { RootStackParamList } from '../../../../interface/type';
import { getGameReview } from '../../action/getGameReview';
import { MapClub } from '../../../../interface';
import ScreenWrapper from '../../../../components/layout/ScreenWrapper';

const GameReview = ({
    navigation,
    route,
}: {
    navigation: NativeStackNavigationProp<RootStackParamList>;
    route: RouteProp<RootStackParamList, 'GameReview'>;
}) => {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const { club }: { club: MapClub } = route.params;
    const [refreshing, setRefreshing] = useState(false);
    const [currentPage, setCurrentPage] = useState(0);
    const [totalPage, setTotalPage] = useState(0);
    const [hasNextPage, setHasNextPage] = useState(false);

    useLayoutEffect(() => {
        handleGameReview();
    }, []);

    const handleGameReview = async (isRefreshing = false) => {
        if (isRefreshing) {
            setRefreshing(true);
            setCurrentPage(0);
        }
        if (currentPage === 0) actions.setAppSkeltonLoader(true);
        const review = await getGameReview({
            userId: user?.id,
            clubId: club.id,
            page: isRefreshing ? 0 : currentPage,
            limit: 10,
        });
        if (review.status) {
            if (currentPage === 0 || isRefreshing) {
                actions.setGameReview(review.data.reviews);
            } else {
                actions.setGameReview([...state.gameReview, ...review.data.reviews]);
            }
            setCurrentPage(review.data.pagination.currentPage + 1);
            setTotalPage(review.data.pagination.totalPages);
            setHasNextPage(review.data.pagination.hasNextPage);
        }
        actions.setAppSkeltonLoader(false);
        setRefreshing(false);
    };

    const handleReload = () => {
        if (currentPage < totalPage && hasNextPage) {
            handleGameReview();
        }
    };

    return (
        <ScreenWrapper>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={styles.container}>
                <ProfileHeader
                    title={'Game Reviews'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_15,
                    }}
                />
                {!state.appSkeltonLoader ? (
                    <View style={styles.contentContainer}>
                        <FlatList
                            data={state.gameReview}
                            renderItem={({ item }) => <GameReviewListingCard item={item} navigation={navigation} />}
                            keyExtractor={(item) => item.game_id.toString()}
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={{
                                marginVertical: Spacing.SCALE_12,
                                paddingBottom: Spacing.SCALE_12,
                            }}
                            refreshControl={
                                <RefreshControl
                                    refreshing={refreshing}
                                    onRefresh={() => {
                                        setCurrentPage(0);
                                        handleGameReview(true);
                                    }}
                                />
                            }
                            onEndReachedThreshold={0.5}
                            onEndReached={() => handleReload()}
                        />
                    </View>
                ) : (
                    <RequestScreenSkelton screen={'Common'} />
                )}
            </View>
        </ScreenWrapper>
    );
};

export default GameReview;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
    },
    headerTitleStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
    },
    contentContainer: {
        flex: 1,
        backgroundColor: colors.screenBG,
        paddingHorizontal: Spacing.SCALE_16,
        marginBottom: Spacing.SCALE_4,
    },
    reviewItem: {
        backgroundColor: colors.whiteRGB,
        borderRadius: 8,
        padding: Spacing.SCALE_16,
        marginBottom: Spacing.SCALE_16,
    },
    userInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_12,
    },
    profilePhoto: {
        width: 40,
        height: 40,
        borderRadius: 20,
    },
    profilePhotoFallback: {
        backgroundColor: colors.lightBlack,
        justifyContent: 'center',
        alignItems: 'center',
    },
    profilePhotoText: {
        color: colors.whiteRGB,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
    },
    userTextInfo: {
        marginLeft: Spacing.SCALE_12,
    },
    userName: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        color: colors.lightBlack,
        marginBottom: 2,
    },
    gameDate: {
        fontSize: Typography.FONT_SIZE_12,
        color: colors.darkgray,
    },
    reviewText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        marginBottom: Spacing.SCALE_12,
        lineHeight: 20,
    },
    gameImage: {
        width: '100%',
        height: 200,
        borderRadius: 8,
    },
});

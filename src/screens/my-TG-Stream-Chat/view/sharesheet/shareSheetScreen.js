import React, { useContext, useEffect, useState } from 'react';
import { FlatList, Image, SafeAreaView, StyleSheet, Text, View, Platform } from 'react-native';
import moment from 'moment';
import { useNavigation } from '@react-navigation/native';
import firebase from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/storage';
import { createShimmerPlaceholder } from 'react-native-shimmer-placeholder';
import LinearGradient from 'react-native-linear-gradient';
import RNFS from 'react-native-fs';

import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import StreamChatButton from '../../../../components/buttons/StreamChatButton';
import { AuthContext } from '../../../../context/AuthContext';
import { StreamChatContext } from '../../../../context/StreamChatContext';
import StreamChatSearchInput from '../../../my-TG-friends/view/StreamChatSearchInput';
import {
    ADMIN_CREATED_GROUP,
    MY_TG_GROUP,
    ONE_TO_ONE,
    SYSTEM_PRIVATE_NETWORK,
    SYSTEM_THOUSAND_GREENS_PUBLIC,
    USER_CREATED_GROUP,
} from '../../client';
import RenderItem from './RenderItem';
import EmptyStateScreen from '../../../my-TG-Stream-Chat/forwardMessage/view/EmptyStateScreen';
import StreamHeader from '../../../../components/layout/StreamHeader';
import { GlobalContext } from '../../../../context/contextApi';
import { attachmentObj } from '../../constants';
import { sendMessageApi } from './sendImageApi';
import TGLoader from '../../../../components/layout/TGLoader';

const ShareSheetScreen = () => {
    const navigation = useNavigation();
    const { user } = useContext(AuthContext);
    const { client, shareImageObject, setShareImageObject } = useContext(StreamChatContext);
    const [loading, setLoading] = useState(false);
    const [activityLoading, setActivityLoading] = useState(false);
    const [toggleCheckBox, setToggleCheckBox] = useState(false);
    const [selectedData, setSelectedData] = useState([]);
    const [allChannels, setAllChannels] = useState(null);
    const [searchData, setSearchData] = useState('');
    const [limit, setLimit] = useState(30);
    const [currentPage, setCurrentPage] = useState(0);
    const [checkBoxIcon, setCheckBoxIcon] = useState(false);
    const [filteredChannel, setFilteredChannel] = useState(null);
    const [firebaseGeneratedImgArr, setFirebaseGeneratedImgArr] = useState([]);
    const ShimmerPlaceholder = createShimmerPlaceholder(LinearGradient);

    const sort = { last_message_at: -1 };
    const options = {
        limit: limit,
        offset: limit * currentPage,
    };

    const getChannels = async (filters) => {
        const channels = await client.queryChannels(filters, sort, options);
        if (currentPage === 0) {
            filterChannel(channels);
            setAllChannels(channels);
        } else {
            filterChannel(channels);
            setAllChannels([...allChannels, ...channels]);
        }
    };

    const filterChannel = (allChannels) => {
        let filterChannels = allChannels.filter((channel) => {
            if (channel?.type === ONE_TO_ONE) {
                //Filter out blocked channels
                if (channel?.data?.frozen) {
                    return false;
                }

                if (!channel?.data?.isFriends && channel?.state?.messages?.length) {
                    return true;
                } else if (!channel?.data?.isFriends && !channel?.state?.messages?.length) {
                    return false;
                }
                return true;
            }
            return true;
        });
        if (currentPage === 0) {
            setFilteredChannel(filterChannels);
        } else {
            setFilteredChannel([...filteredChannel, ...filterChannels]);
        }
    };

    useEffect(() => {
        if (searchData != '') {
            setCurrentPage(0);
            const filters = {
                members: { $in: [user?.id] },
                $or: [
                    {
                        $and: [
                            {
                                type: { $eq: ONE_TO_ONE },
                            },
                            {
                                'member.user.name': { $autocomplete: searchData },
                            },
                            {
                                $or: [
                                    {
                                        isFriends: { $eq: false },
                                        last_message_at: {
                                            $lte: moment(moment()).add(2, 'hours'),
                                        },
                                        member_count: { $eq: 2 },
                                    },
                                    {
                                        isFriends: { $eq: true },
                                        member_count: { $eq: 2 },
                                    },
                                ],
                            },
                            {
                                frozen: { $eq: false },
                            },
                        ],
                    },
                    {
                        $and: [
                            {
                                type: {
                                    $in: [USER_CREATED_GROUP, SYSTEM_THOUSAND_GREENS_PUBLIC, SYSTEM_PRIVATE_NETWORK],
                                },
                            },
                            {
                                name: { $autocomplete: searchData },
                            },
                        ],
                    },
                    {
                        hidden: { $eq: true },
                    },
                    {
                        hidden: { $eq: false },
                    },
                ],
            };
            getChannels(filters);
        } else {
            const filters = {
                members: { $in: [user?.id] },
                $or: [
                    {
                        $and: [
                            {
                                type: { $eq: ONE_TO_ONE },
                            },
                            {
                                $or: [
                                    {
                                        isFriends: { $eq: false },
                                        last_message_at: {
                                            $lte: moment(moment()).add(2, 'hours'),
                                        },
                                        member_count: { $eq: 2 },
                                    },
                                    {
                                        isFriends: { $eq: true },
                                        member_count: { $eq: 2 },
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        type: {
                            $in: [
                                USER_CREATED_GROUP,
                                SYSTEM_THOUSAND_GREENS_PUBLIC,
                                SYSTEM_PRIVATE_NETWORK,
                                ADMIN_CREATED_GROUP,
                                MY_TG_GROUP,
                            ],
                        },
                    },
                    {
                        hidden: { $eq: true },
                    },
                    {
                        hidden: { $eq: false },
                    },
                ],
            };
            getChannels(filters);
        }
    }, [searchData, currentPage]);

    // useEffect call for upload images to the firebase
    useEffect(() => {
        if (shareImageObject?.length) {
            uploadPhoto();
        }
        return () => {
            setShareImageObject([]);
            setFirebaseGeneratedImgArr([]);
        };
    }, [shareImageObject]);

    // This function is used to take images from firebase and set to a local state
    const uploadPhoto = async () => {
        setActivityLoading(true);
        for (let i = 0; i < shareImageObject?.length; i++) {
            try {
                const photoURL = uploadImage(shareImageObject[i]).then((res) => {
                    setActivityLoading(false);
                    setFirebaseGeneratedImgArr((prevTemp) => [...prevTemp, res]);
                });
            } catch (e) {
                console.log('error', e);
            }
        }
    };

    // OPTIMIZED: Simple and reliable image upload function
    async function uploadImage({ path }) {
        try {
            console.log("� [OPTIMIZED] Starting upload for path:", path);

            // Step 1: Clean the path
            let cleanPath = path.startsWith('file://') ? path.replace('file://', '') : path;
            console.log("� [OPTIMIZED] Clean path:", cleanPath);

            // Step 2: Check if file exists
            const exists = await RNFS.exists(cleanPath);
            console.log("� [OPTIMIZED] File exists:", exists);

            if (!exists) {
                // Try to find the file in TGSharedImages directory
                const fileName = cleanPath.split('/').pop(); // Get filename

                // Try App Group locations first (using correct App Group ID)
                const appGroupPaths = [
                    `/private/var/mobile/Containers/Shared/AppGroup/group.com.thousand-greens.shared/TGSharedImages/${fileName}`,
                    `/var/mobile/Containers/Shared/AppGroup/group.com.thousand-greens.shared/TGSharedImages/${fileName}`,
                ];

                let foundInAppGroup = false;
                for (const appGroupPath of appGroupPaths) {
                    const appGroupExists = await RNFS.exists(appGroupPath);
                    if (appGroupExists) {
                        cleanPath = appGroupPath;
                        foundInAppGroup = true;
                        console.log("OPTIMIZED: Found in App Group:", appGroupPath);
                        break;
                    }
                }

                if (!foundInAppGroup) {
                    // Fallback to Documents directory
                    const documentsPath = RNFS.DocumentDirectoryPath;
                    const alternativePath = `${documentsPath}/TGSharedImages/${fileName}`;

                console.log("� [OPTIMIZED] Trying alternative path:", alternativePath);
                const altExists = await RNFS.exists(alternativePath);

                if (altExists) {
                    cleanPath = alternativePath;
                    console.log("� [OPTIMIZED] ✅ Found file at alternative path");
                } else {
                    throw new Error(`File not found: ${path}`);
                }
            }

            // Step 3: Validate file
            const fileInfo = await RNFS.stat(cleanPath);
            if (!fileInfo.isFile() || fileInfo.size === 0) {
                throw new Error(`Invalid file: ${cleanPath}`);
            }
            console.log("� [OPTIMIZED] File validated - Size:", fileInfo.size);

            // Step 4: Upload to Firebase
            const firebaseURL = `stream/shared-images-${firebase.auth().currentUser.uid}-${Date.now()}`;
            const storageRef = firebase.storage().ref().child(firebaseURL);

            await storageRef.putFile(cleanPath);
            const downloadURL = await storageRef.getDownloadURL();

            console.log("� [OPTIMIZED] ✅ Upload successful:", downloadURL);
            return downloadURL;

        } catch (error) {
            console.log('� [OPTIMIZED] ❌ Upload failed:', error.message);
            throw error;
        }
    }

    //Method for handle send Message to different channels
    const handleSendButton = () => {
        setLoading(true);
        let tempImgObjectArr = [];
        firebaseGeneratedImgArr?.map((image_url) => {
            tempImgObjectArr.push({ ...attachmentObj, image_url });
        });
        if (tempImgObjectArr?.length) {
            const payload = {
                userId: user?.id,
                channels: selectedData,
                attachments: tempImgObjectArr,
            };
            sendMessageApi(payload, setLoading, navigation);
        }
    };

    const handleRenderImage = () => {
        return (
            <View
                style={{
                    paddingVertical: 20,
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                }}>
                {firebaseGeneratedImgArr?.map((uri, index) => {
                    return (
                        <Image
                            key={index}
                            style={{
                                height: 45,
                                width: 45,
                                borderRadius: 8,
                                marginRight: 5,
                            }}
                            source={{
                                uri: uri,
                            }}
                        />
                    );
                })}
            </View>
        );
    };

    return (
        <>
            <StreamHeader screenName={`Share Message To`} />
            {!activityLoading ? (
                <>
                    <View style={{ flex: 1 }}>
                        <View style={styles.popupWrapper}>
                            <StreamChatSearchInput
                                searchState={[searchData, setSearchData]}
                                searchBoxWrapperStyle={{ marginHorizontal: 0 }}
                                searchBoxStyle={{ width: Size.SIZE_250 }}
                            />
                            <View style={styles.counterWrapper}>
                                <Text style={styles.text1}>Recent Chats</Text>
                                <Text style={styles.text1}>{`${selectedData?.length}/10`}</Text>
                            </View>
                            {filteredChannel?.length > 0 ? (
                                <FlatList
                                    data={filteredChannel}
                                    renderItem={(item) => (
                                        <RenderItem
                                            data={item}
                                            selectedState={[selectedData, setSelectedData]}
                                            checkboxState={[toggleCheckBox, setToggleCheckBox]}
                                            allChannels={allChannels}
                                            checkBoxIconHandler={[checkBoxIcon, setCheckBoxIcon]}
                                            searchData={searchData}
                                        />
                                    )}
                                    showsVerticalScrollIndicator={false}
                                    keyExtractor={(item) => item.id}
                                    onEndReached={() => {
                                        if (allChannels?.length === currentPage * limit + 30 || allChannels === null) {
                                            setCurrentPage(currentPage + 1);
                                        }
                                    }}
                                    onEndReachedThreshold={0.8}
                                    initialNumToRender={30}
                                    style={{ flex: 1 }}
                                />
                            ) : (
                                !loading && !activityLoading && <EmptyStateScreen />
                            )}
                        </View>
                    </View>
                    <View
                        style={{
                            paddingBottom: Spacing.SCALE_10,
                            paddingHorizontal: Spacing.SCALE_12,
                        }}>
                        {handleRenderImage()}
                        {!activityLoading && (
                            <StreamChatButton
                                text="Send"
                                label="Send"
                                loading={loading}
                                onPress={handleSendButton}
                                btnDissable={selectedData?.length == 0}
                            />
                        )}
                    </View>
                </>
            ) : (
                <SafeAreaView style={{ flex: 1 }}>
                    <ShimmerPlaceholder
                        isInteraction
                        duration={2000}
                        style={{ width: '90%', height: 40, borderRadius: 10, marginTop: 10, alignSelf: 'center' }}
                    />
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((item, index) => {
                        return (
                            <View
                                key={index.toString()}
                                style={{
                                    flex: 1,
                                    flexDirection: 'row',
                                    paddingHorizontal: Spacing.SCALE_16,
                                    alignItems: 'center',
                                    marginVertical: Spacing.SCALE_8,
                                }}>
                                <ShimmerPlaceholder
                                    isInteraction
                                    duration={2000}
                                    style={{ width: 40, height: 40, borderRadius: 50, marginRight: 10 }}
                                />
                                <ShimmerPlaceholder
                                    isInteraction
                                    duration={2000}
                                    style={{
                                        width: '80%',
                                        height: Size.SIZE_40,
                                        borderRadius: Size.SIZE_6,
                                    }}
                                />
                                <ShimmerPlaceholder
                                    isInteraction
                                    duration={2000}
                                    style={{
                                        width: Size.SIZE_15,
                                        height: Size.SIZE_15,
                                        borderRadius: Size.SIZE_4,
                                        marginLeft: 10,
                                    }}
                                />
                            </View>
                        );
                    })}
                </SafeAreaView>
            )}
        </>
    );
};

export default ShareSheetScreen;

const styles = StyleSheet.create({
    modal: {
        paddingHorizontal: 0,
        marginHorizontal: 0,
        paddingVertical: 0,
        marginVertical: 0,
    },
    popupWrapper: {
        flex: 1,
        backgroundColor: 'rgba(242, 242, 242, 1)',
        borderTopLeftRadius: Size.SIZE_20,
        borderTopRightRadius: Size.SIZE_20,
        paddingHorizontal: Spacing.SCALE_12,
    },
    modalBackgroundStyle: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    nameInitialStyle: {
        width: Size.SIZE_37,
        height: Size.SIZE_37,
        borderRadius: Size.SIZE_50,
        backgroundColor: 'rgba(9, 128, 137, 1)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_20,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '600',
        color: 'rgba(255, 255, 255, 1)',
    },
    renderComponentContainer: {
        paddingVertical: Spacing.SCALE_10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    box1: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    text: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        lineHeight: Size.SIZE_20,
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(51, 51, 51, 1)',
        marginLeft: Spacing.SCALE_28,
    },
    checkboxContainer: {
        width: Size.SIZE_15,
        height: Size.SIZE_15,
        borderColor: 'rgba(9, 128, 137, 1)',
        borderRadius: Size.SIZE_4,
    },
    hearder: {
        fontSize: Typography.FONT_SIZE_21,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        lineHeight: Spacing.SCALE_24,
        color: 'rgba(51, 51, 51, 1)',
    },
    text1: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        lineHeight: Spacing.SCALE_16,
        color: 'rgba(9, 128, 137, 1)',
        marginBottom: Spacing.SCALE_10,
    },
    imageContainer: {
        width: 46,
        height: 46,
        borderRadius: 50,
        backgroundColor: 'rgba(9, 128, 137, 1)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageTextStyle: {
        textAlign: 'center',
        fontSize: 25,
        color: 'rgba(255, 255, 255, 1)',
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
    },
    textWrapper: {
        width: Spacing.SCALE_250,
        alignItems: 'center',
        marginTop: Spacing.SCALE_10,
    },
    loaderStyle: {
        bottom: 0,
        right: 0,
        left: 0,
    },
    crossIconWrapper: {
        padding: Spacing.SCALE_10,
    },
    counterWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
});

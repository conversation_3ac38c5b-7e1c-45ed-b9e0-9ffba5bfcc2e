import { Platform, StyleSheet, Text, TouchableOpacity, View, StatusBar, Alert, PermissionsAndroid } from 'react-native';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import Animated, {
    SlideInUp,
    SlideOutDown,
    useAnimatedStyle,
    useSharedValue,
    withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// theme, components, utils, hooks imports
import { colors } from '../../../theme/theme';
import ProfileHeader from '../../../components/layout/ProfileHeader';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import useOfferState from '../../../hooks/useOfferState';
import TGSearchNew from '../../my-TG-friends/view/TGSearch';
import OfferView from './OfferView';
import { AuthContext } from '../../../context/AuthContext';
import CustomSearchModal from '../../../components/CustomSearchModal';
import { handleTimeFormat } from '../../../components/timeFormatComponent/handleTimeFormat';
import OfferFilter from './offerFilter/OfferFilter';
import TGCustomModalView from '../../../components/modals/TGCustomModalView';
import { CrossIcon } from '../../../assets/svg';
import { handleCreateOffer } from '../action/handleCreateOffer';
import { RootStackParamList } from '../../../interface/type';
import { NavigationProp, RouteProp, useFocusEffect } from '@react-navigation/native';
import { CLUB_MEMBER_COUNT_FILTER, SEARCH_OFFERS_BY_LOCATION_CLUB_AND_DATE } from '../../../utils/constants/strings';
import { CLUB_PERCENTAGE_FILTER } from '../../../utils/constants/strings';
import { ALL } from '../../../utils/constants/strings';
import { GlobalContext } from '../../../context/contextApi';
import { apiServices } from '../../../service/apiServices';
import showToast from '../../../components/toast/CustomToast';
import { Offer } from '../../../interface';
import ScreenWrapper from '../../../components/layout/ScreenWrapper';

const OfferScreen = ({
    navigation,
    route,
}: {
    navigation: NavigationProp<RootStackParamList>;
    route: RouteProp<RootStackParamList, 'Offers'>;
}) => {
    const { user } = useContext(AuthContext);
    const insets = useSafeAreaInsets();
    const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
    const {
        clubs,
        activeView,
        setActiveView,
        searchTerm,
        setSearchTerm,
        zoomLevel,
        setZoomLevel,
        centerCoordinates,
        setCenterCoordinates,
        showSearch,
        setShowSearch,
        offersMapMarkers,
        mapBoundaries,
        setMapBoundaries,
        selectedClub,
        setSelectedClub,
        searchedClubList,
        locations,
        selectedRegion,
        setSelectedRegion,
        dateRange,
        setDateRange,
        offers,
        setSearchType,
        renderedOffers,
        filter,
        setFilter,
        showFilterOption,
        setShowFilterOption,
        getMapClubs,
        searchedClub,
        setSearchedClub,
        isMarkerClicked,
        setIsMarkerClicked,
        setFilterActive,
        myOffers,
        setMyOffers,
    } = useOfferState({ user });

    // Animation values
    const contentOpacity = useSharedValue(1);
    const contentTranslateY = useSharedValue(0);
    const { state } = useContext(GlobalContext);
    const [isFilterApplied, setIsFilterApplied] = useState(false);
    const { actions } = useContext(GlobalContext);
    const [locationType, setLocationType] = useState<string>('');

    const checkAndroidPerms = () => {
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
            .then((permission: any) => {
                setLocationPermissionGranted(permission === 'denied' ? false : permission);
            })
            .catch((err) => {});
    };

    useFocusEffect(
        useCallback(() => {
            Platform.OS === 'android' && checkAndroidPerms();
        }, [offersMapMarkers]),
    );

    // Animated styles
    const contentAnimatedStyle = useAnimatedStyle(() => {
        return {
            opacity: contentOpacity.value,
            transform: [{ translateY: contentTranslateY.value }],
        };
    });

    useEffect(() => {
        for (let key in filter) {
            if (
                (filter as any)[key] === true ||
                filter[CLUB_MEMBER_COUNT_FILTER] !== ALL ||
                filter[CLUB_PERCENTAGE_FILTER] !== ALL
            ) {
                setIsFilterApplied(true);
                break;
            } else {
                setIsFilterApplied(false);
            }
        }
    }, [filter]);

    const handleSearchPress = () => {
        // Animate content out
        contentOpacity.value = withTiming(0, { duration: 300 });
        contentTranslateY.value = withTiming(-20, { duration: 300 });
        setShowSearch(true);
    };

    const handleSearchClose = () => {
        // Animate content back in
        contentOpacity.value = withTiming(1, { duration: 300 });
        contentTranslateY.value = withTiming(0, { duration: 300 });
        setShowSearch(false);
        actions.setIsMapSearchActive(false);
    };

    const canCreateOffer = async () => {
        let res = await handleCreateOffer(user);
        if (res.status) {
            if (res?.canCreate) {
                navigation.navigate('Create Offer', {
                    createFrom: '',
                    my_tg_group_id: null,
                    my_tg_group: '',
                    callBack: getMapClubs,
                });
            } else Alert.alert(res?.message);
        }
    };

    const handleRequestAgainstOffer = async (item: Offer) => {
        actions?.setAppLoader(true);
        const res = await apiServices.checkCanCreateRequest(user, !state.allFriendsId[item?.user_id]);
        if (res?.canCreate) {
            const offerDetailsRes = await apiServices.getOfferDetails({
                userId: user?.id,
                offerId: String(item.offer_id || ''),
            });
            if (offerDetailsRes.status) {
                actions?.setAppLoader(false);
                navigation.navigate('Request Against Offer', {
                    offer: offerDetailsRes?.data,
                    callBack: () => {
                        getMapClubs();
                    },
                });
            }
        } else {
            actions?.setAppLoader(false);
            navigation.navigate('DeleteChannelConfirmationPopup', {
                popupSubText: res?.message,
                firstBtnLabel: 'Cancel',
                secondBtnLabel: 'Ok',
            });
        }
    };

    const handleEditOffer = async (offer: Offer) => {
        actions?.setAppLoader(true);
        const offerDetailsRes = await apiServices.getOfferDetails({
            userId: user?.id,
            offerId: String(offer.offer_id || ''),
        });
        if (offerDetailsRes?.status) {
            actions?.setAppLoader(false);
            navigation.navigate('Edit Offer', {
                offer: offerDetailsRes?.data,
                refresh: getMapClubs,
            });
        }
    };

    const handleDeleteOffer = async (offer: Offer) => {
        const deleteOffer = () => {
            const deleteOfferParams = {
                userId: user?.id,
                offerId: offer?.id,
            };
            apiServices.deleteOffer(deleteOfferParams).then((res) => {
                if (res?.status) {
                    getMapClubs();
                } else {
                    showToast({});
                }
            });
        };
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: deleteOffer,
            popupHeader: 'Delete Offer',
            popupSubText: 'Are you sure you would like to delete your offer?',
            firstBtnLabel: 'Dismiss',
            secondBtnLabel: 'Delete',
        });
    };

    const handleOfferDetailPress = (item: Offer) => {
        navigation.navigate('OfferDetails', {
            offerID: item.offer_id,
            prevScreenCallBack: getMapClubs,
        });
    };

    const HeaderScreenWrapper = state?.isMapViewExpanded ? View : ScreenWrapper;

    return (
        <>
            <StatusBar barStyle="dark-content" backgroundColor={colors.whiteRGB} />
            <HeaderScreenWrapper style={[{ flex: 1 }, searchTerm && { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
                <Animated.View style={[styles.container]}>
                    {!state?.isMapViewExpanded && (Platform.OS === 'android' ? locationPermissionGranted : true) && (
                        <View>
                            <ProfileHeader
                                title={'Offers'}
                                headerTitleStyle={styles.headerTitleStyle}
                                backButtonFillColor={colors.lightBlack}
                                containerStyle={{
                                    backgroundColor: colors.whiteRGB,
                                    paddingBottom: Spacing.SCALE_15,
                                }}
                                activeView={activeView}
                                setActiveView={setActiveView}
                                showToggleIcon={true}
                                showFilter={true}
                                onClick={() => {
                                    setShowFilterOption(true);
                                }}
                                showOfferIcon={true}
                                handleOfferIconClick={canCreateOffer}
                                filterTealDot={isFilterApplied}
                            />
                        </View>
                    )}
                    <View style={{ flex: 1, backgroundColor: colors.screenBG }}>
                        {!state?.isMapViewExpanded &&
                            (Platform.OS === 'android' ? locationPermissionGranted : true) && (
                                <View
                                    style={
                                        activeView === 'MAP'
                                            ? {
                                                  position: 'absolute',
                                                  zIndex: 1000,
                                                  alignSelf: 'center',
                                              }
                                            : {}
                                    }>
                                    <TGSearchNew
                                        searchState={['', () => {}]}
                                        searchBoxWrapperStyle={{
                                            marginBottom: Spacing.SCALE_7,
                                        }}
                                        placeholder={SEARCH_OFFERS_BY_LOCATION_CLUB_AND_DATE}
                                        editable={false}
                                        onPress={handleSearchPress}
                                    />
                                    {(searchedClub || dateRange.startDate) && (
                                        <View style={styles.selectedClubWrapper}>
                                            {searchedClub && (
                                                <View style={styles.selectedClubContainer}>
                                                    <View style={{ paddingVertical: Spacing.SCALE_9 }}>
                                                        <Text style={styles.selectedClubText}>
                                                            {searchedClub?.properties.name?.length > 10
                                                                ? searchedClub?.properties.name.slice(0, 10) + '...'
                                                                : searchedClub?.properties.name}
                                                        </Text>
                                                    </View>
                                                    <TouchableOpacity
                                                        style={styles.crossIconContainer}
                                                        onPress={() => {
                                                            setSearchTerm('');
                                                            setSelectedClub(null);
                                                            setSearchedClub(null);
                                                        }}>
                                                        <View style={styles.crossIconWrapper}>
                                                            <CrossIcon height={Size.SIZE_14} width={Size.SIZE_14} />
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                            )}
                                            {dateRange.startDate && (
                                                <View style={styles.selectedClubContainer}>
                                                    <Text style={styles.selectedClubText}>
                                                        {dateRange.startDate && !dateRange.endDate
                                                            ? handleTimeFormat(dateRange.startDate)
                                                            : dateRange.startDate && dateRange.endDate
                                                            ? handleTimeFormat(dateRange.startDate) +
                                                              ' - ' +
                                                              handleTimeFormat(dateRange.endDate)
                                                            : null}
                                                    </Text>
                                                    <TouchableOpacity
                                                        style={styles.crossIconContainer}
                                                        onPress={() => setDateRange({ startDate: '', endDate: '' })}>
                                                        <View style={styles.crossIconWrapper}>
                                                            <CrossIcon height={Size.SIZE_14} width={Size.SIZE_14} />
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                            )}
                                        </View>
                                    )}
                                </View>
                            )}
                        <OfferView
                            clubs={clubs}
                            centerCoordinates={centerCoordinates}
                            setCenterCoordinates={setCenterCoordinates}
                            zoomLevel={zoomLevel}
                            setZoomLevel={setZoomLevel}
                            showSearch={showSearch}
                            setShowSearch={setShowSearch}
                            offersMapMarkers={offersMapMarkers}
                            mapBoundaries={mapBoundaries}
                            setMapBoundaries={setMapBoundaries}
                            activeView={activeView}
                            selectedClub={selectedClub}
                            setSelectedClub={setSelectedClub}
                            searchedClubList={searchedClubList}
                            locations={locations}
                            searchTerm={searchTerm}
                            setActiveView={setActiveView}
                            selectedRegion={selectedRegion || { lat: 0, lng: 0 }}
                            setSelectedRegion={setSelectedRegion}
                            offers={offers}
                            renderedOffers={renderedOffers}
                            dateRange={dateRange}
                            setDateRange={setDateRange}
                            searchedClub={searchedClub}
                            setSearchedClub={setSearchedClub}
                            routeClub={route?.params?.club || null}
                            isMarkerClicked={isMarkerClicked}
                            setIsMarkerClicked={setIsMarkerClicked}
                            handleRequestAgainstOffer={handleRequestAgainstOffer}
                            handleEditOffer={handleEditOffer}
                            handleDeleteOffer={handleDeleteOffer}
                            handleOfferDetailPress={handleOfferDetailPress}
                            navigation={navigation}
                            showFilterOption={showFilterOption}
                            myOffers={myOffers}
                            setMyOffers={setMyOffers}
                            locationType={locationType}
                        />
                    </View>
                </Animated.View>
                {showSearch && (
                    <Animated.View
                        entering={SlideInUp.duration(300)}
                        exiting={SlideOutDown.duration(300)}
                        style={styles.modalContainer}>
                        <CustomSearchModal
                            onClose={handleSearchClose}
                            searchTerm={searchTerm}
                            setSearchTerm={setSearchTerm}
                            handleSearchPress={handleSearchPress}
                            searchedClubList={searchedClubList}
                            locations={locations}
                            activeView={activeView}
                            selectedRegion={selectedRegion}
                            setSelectedRegion={setSelectedRegion}
                            dateRange={dateRange}
                            setSelectedClub={setSelectedClub}
                            setDateRange={setDateRange}
                            setSearchType={setSearchType}
                            searchedClub={searchedClub}
                            setSearchedClub={setSearchedClub}
                            setActiveView={setActiveView}
                            setLocationType={setLocationType}
                        />
                    </Animated.View>
                )}
            </HeaderScreenWrapper>
            {showFilterOption && (
                <TGCustomModalView
                    isVisible={showFilterOption}
                    style={{ flex: 1 }}
                    onPressOutSide={() => {}}
                    screen="Map">
                    <OfferFilter
                        filterState={[filter, setFilter]}
                        setFilterActive={setFilterActive}
                        setModal={() => setShowFilterOption(false)}
                    />
                </TGCustomModalView>
            )}
        </>
    );
};

export default OfferScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
    },
    headerTitleStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
    },
    contentContainer: {
        flex: 1,
        backgroundColor: colors.screenBG,
        padding: Spacing.SCALE_16,
        marginBottom: Spacing.SCALE_4,
    },
    modalContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: colors.transparentRgba,
    },
    selectedClubContainer: {
        backgroundColor: colors.whiteRGB,
        alignSelf: 'center',
        borderRadius: Size.SIZE_10,
        paddingHorizontal: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_12,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
    },
    selectedClubText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    selectedClubWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: Spacing.SCALE_10,
        columnGap: Spacing.SCALE_8,
    },
    crossIconContainer: {
        height: Size.SIZE_32,
        width: Size.SIZE_20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    crossIconWrapper: {
        height: Size.SIZE_16,
        width: Size.SIZE_16,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.lightGrey,
        borderRadius: Size.SIZE_20,
    },
});

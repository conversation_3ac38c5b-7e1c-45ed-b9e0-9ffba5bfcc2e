import {
    Alert,
    Dimensions,
    ImageBackground,
    Platform,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    SafeAreaView,
} from 'react-native';
import React, { useContext } from 'react';
import { useNavigation } from '@react-navigation/native';
import auth from '@react-native-firebase/auth';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
const CleverTap = require('clevertap-react-native');

import {
    SettingIcon,
    GolfIcon,
    NotificationIcon,
    TGReferral,
    ProfileIcon,
    LogoutIcon,
    FounderClubIcon,
    RightIconNew,
} from '../../../assets/svg/index';
import { Back } from '../../../assets/images/svg';
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import EditProfilePhoto from '../../../components/fields/EditProfilePhoto';
import TGAmbassadorLable from '../../../forms/profile/views/TGAmbassadorLable';
import { AuthContext } from '../../../context/AuthContext';
import SocialLinkComponent from './SocialLinkComponent';
import { tiers } from '../../../utils/constants/constants';

const sections = [
    {
        name: 'Profile',
        label: 'Personal Profile',
        Icon: ProfileIcon,
    },
    {
        name: 'Account Settings',
        label: 'Account Settings',
        Icon: SettingIcon,
    },
    {
        name: 'Golf Clubs',
        label: 'My Golf Clubs',
        Icon: GolfIcon,
    },

    {
        name: 'Notifications Preferences',
        label: 'NotificationPreferences',
        Icon: NotificationIcon,
    },
    {
        name: 'TG Referrals',
        label: 'My Referrals',
        Icon: TGReferral,
    },
    {
        name: 'Logout',
        label: 'Logout',
        Icon: LogoutIcon,
    },
];

const Setting = () => {
    const navigation = useNavigation<NativeStackNavigationProp<any>>();
    const { user } = useContext(AuthContext);

    const handleLogOut = () => {
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: async () => {
                await auth().signOut();
            },
            popupHeader: 'Confirmation',
            popupSubText: 'Are you sure you want to Logout?',
            firstBtnLabel: 'Dismiss',
            secondBtnLabel: 'Logout',
        });
    };

    return (
        <View style={styles.mainContainer}>
            <StatusBar backgroundColor={colors.tealRgb} barStyle="light-content" />
            <View style={styles.bottomSafeArea}>
                <ScrollView style={styles.container} bouncesZoom={false} bounces={false}>
                    <View style={styles.headerContainer}>
                        <ImageBackground
                            source={require('../../../assets/images/SettingBG.png')}
                            style={{ width: '100%', height: Size.SIZE_135, paddingTop: Spacing.SCALE_30 }}>
                            <View style={styles.headerWrapperStyle}>
                                <TouchableOpacity
                                    style={styles.headerBackButtonWrapper}
                                    onPress={() => {
                                        navigation.goBack();
                                    }}>
                                    <Back fill={colors.whiteRGB} />
                                </TouchableOpacity>
                                <Text style={styles.headerText}>Profile</Text>
                            </View>
                        </ImageBackground>
                    </View>
                    <View style={styles.cardWrapper}>
                        <EditProfilePhoto photo={user.profilePhoto} user={user} />
                    </View>
                    <View style={styles.box}>
                        <View style={{ marginTop: Spacing.SCALE_26, alignItems: 'center' }}>
                            <Text style={styles.nameWrapper}>
                                {user.first_name} {user.last_name}
                            </Text>
                            <Text style={styles.userNameWrapper}>{user.username}</Text>
                            <View style={styles.tierWrapper}>
                                <Text style={styles.tierTextStyle}>{tiers[user?.tier]}</Text>
                            </View>
                            {user?.tg_ambassador_visibility && (
                                <TGAmbassadorLable containerStyle={{ marginTop: Spacing.SCALE_12 }} />
                            )}
                            <View style={styles.flexDirectionRow}>
                                {user?.is_tg_founder && (
                                    <>
                                        <View style={styles.flexDirectionRow}>
                                            <FounderClubIcon width={Size.SIZE_12} height={Size.SIZE_12} />
                                            <Text style={styles.founderClubText}>Founder club member</Text>
                                        </View>
                                        <View style={styles.dot} />
                                    </>
                                )}
                                <Text style={styles.membershipStyle}>
                                    Member since {new Date(user.created_at).getFullYear()}
                                </Text>
                            </View>
                        </View>
                        <View style={{ paddingHorizontal: Spacing.SCALE_16, marginBottom: Spacing.SCALE_10 }}>
                            <SocialLinkComponent facebookLink={user?.facebook} linkedinLink={user?.linkedin} />
                        </View>
                        <View style={styles.dividerLine} />
                        <View
                            style={{
                                padding: Spacing.SCALE_16,
                            }}>
                            {sections.map(({ label, Icon, name }, index) => {
                                const isLastItem = index === sections.length - 1;
                                return (
                                    <View key={index}>
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (name === 'Logout') {
                                                    handleLogOut();
                                                } else {
                                                    navigation.navigate(label);
                                                    CleverTap.recordEvent(name);
                                                }
                                            }}
                                            style={{
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                justifyContent: 'space-between',
                                                paddingVertical: Spacing.SCALE_11,
                                                paddingTop: index === 0 ? 0 : Spacing.SCALE_11,
                                            }}>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    columnGap: Spacing.SCALE_8,
                                                }}>
                                                <Icon width={Size.SIZE_25} height={Size.SIZE_25} />
                                                <Text style={styles.sectionText}>{name}</Text>
                                            </View>
                                            {name !== 'Logout' && (
                                                <RightIconNew width={Size.SIZE_16} height={Size.SIZE_16} />
                                            )}
                                        </TouchableOpacity>
                                        {!isLastItem && <View style={styles.dividerLine1} />}
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                </ScrollView>
            </View>
        </View>
    );
};

export default Setting;

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: colors.white,
    },
    topSafeArea: {
        flex: 0,
        backgroundColor: colors.tealRgb,
    },
    bottomSafeArea: {
        flex: 1,
        backgroundColor: colors.white,
    },
    headerContainer: {
        alignItems: 'center',
    },
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    box: {
        backgroundColor: colors.white,
        flex: 1,
    },
    cardWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: Spacing.SCALE_1,
        borderRadius: Size.SIZE_12,
        position: 'absolute',
        top: Platform.OS === 'ios' ? Spacing.SCALE_70 : Spacing.SCALE_80,
        zIndex: 111,
        alignSelf: 'center',
        backgroundColor: colors.white,
        height: Size.SIZE_92,
        width: Size.SIZE_92,
    },
    nameWrapper: {
        marginTop: Spacing.SCALE_20,
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '600',
        color: colors.lightBlack,
        lineHeight: Size.SIZE_24,
        fontFamily: 'Ubuntu-Medium',
    },
    userNameWrapper: {
        fontFamily: 'Ubuntu-Medium',
        marginTop: Spacing.SCALE_5,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        color: colors.darkGreyRgba,
        lineHeight: Size.SIZE_14,
    },
    tierWrapper: {
        backgroundColor: colors.badgeLabelBackgroundColor,
        borderRadius: Size.SIZE_8,
        marginTop: Spacing.SCALE_9,
        alignItems: 'center',
        justifyContent: 'center',
    },
    tierTextStyle: {
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Bold',
        fontSize: Typography.FONT_SIZE_10,
        lineHeight: Size.SIZE_10,
        paddingHorizontal: Spacing.SCALE_10,
        paddingVertical: Spacing.SCALE_8,
        textTransform: 'uppercase',
        alignSelf: 'center',
        marginTop: Platform.OS === 'android' ? Spacing.SCALE_1 : 0,
    },
    membershipStyle: {
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkGreyRgba,
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '500',
        lineHeight: Size.SIZE_12,
        textTransform: 'uppercase',
        marginVertical: Spacing.SCALE_2,
        flexDirection: 'row',
        marginTop: Spacing.SCALE_6,
    },
    membershipPlanStyle: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
        color: colors.darkGreyRgba,
        lineHeight: Size.SIZE_12,
        fontWeight: '500',
        textTransform: 'uppercase',
    },
    flexDirectionRow: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_8,
        marginTop: Spacing.SCALE_5,
    },
    founderClubText: {
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Regular',
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_12,
        fontWeight: '500',
        textTransform: 'uppercase',
    },
    logoutText: {
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Regular',
        color: colors.orange,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
    },
    logoutContainer: {
        paddingHorizontal: Spacing.SCALE_18,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_10,
        marginVertical: Spacing.SCALE_40,
    },
    sectionText: {
        color: colors.dark_charcoal,
        fontFamily: 'Ubuntu-Regular',
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_16,
        fontWeight: '400',
    },
    dividerLine: {
        width: '100%',
        backgroundColor: colors.dividerColor,
        height: Size.SIZE_6,
    },
    dividerLine1: {
        width: '90%',
        backgroundColor: colors.greyRgba,
        height: 1,
        alignSelf: 'center',
        marginLeft: Spacing.SCALE_25,
    },
    headerWrapperStyle: {
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_14,
        flexDirection: 'row',
        alignItems: 'center',
        // columnGap: Spacing.SCALE_16,
    },
    headerText: {
        color: colors.white,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_18,
        fontWeight: '500',
    },
    headerBackButtonWrapper: {
        width: Size.SIZE_25,
        height: Size.SIZE_25,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: Spacing.SCALE_16,
    },
    dot: {
        backgroundColor: colors.darkGreyRgba,
        width: Size.SIZE_4,
        height: Size.SIZE_4,
        borderRadius: Size.SIZE_20,
        marginTop: Spacing.SCALE_3,
    },
    logoutButtonContainer: {
        width: '100%',
        backgroundColor: colors.white,
        paddingBottom: Platform.OS === 'ios' ? Spacing.SCALE_20 : 0,
    },
});

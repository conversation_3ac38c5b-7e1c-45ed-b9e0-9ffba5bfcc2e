import AsyncStorage from '@react-native-async-storage/async-storage';
import { getVersion } from 'react-native-device-info';
import { Platform } from 'react-native';

const VERSION_KEY = 'app_version';
const CACHE_VERSION_KEY = 'cache_version';

/**
 * Cache manager utility to handle automatic cache clearing on app updates
 */
export class CacheManager {
  /**
   * Check if app version has changed and clear cache if needed
   */
  static async checkAndClearCacheOnUpdate(): Promise<void> {
    try {
      const currentVersion = getVersion();
      const storedVersion = await AsyncStorage.getItem(VERSION_KEY);
      
      // If no stored version, this is first launch or cache was cleared
      if (!storedVersion) {
        await this.clearAllCache();
        await AsyncStorage.setItem(VERSION_KEY, currentVersion);
        console.log('CacheManager: First launch detected, cache cleared');
        return;
      }

      // If version has changed, clear cache
      if (storedVersion !== currentVersion) {
        console.log(`CacheManager: Version changed from ${storedVersion} to ${currentVersion}, clearing cache`);
        await this.clearAllCache();
        await AsyncStorage.setItem(VERSION_KEY, currentVersion);
      }
    } catch (error) {
      console.error('CacheManager: Error checking version:', error);
    }
  }

  /**
   * Clear all cached data
   */
  static async clearAllCache(): Promise<void> {
    try {
      // Get all keys from AsyncStorage
      const keys = await AsyncStorage.getAllKeys();
      
      // Filter out essential keys that should not be cleared
      const essentialKeys = [
        VERSION_KEY,
        CACHE_VERSION_KEY,
        'customDeviceId', // Keep device ID
        'hasLaunchedIOS', // Keep iOS launch flag
        'tg-user', // Keep user data
        'is-admin', // Keep admin status
        'tg-admin-id', // Keep admin ID
      ];

      // Keys to clear (all keys except essential ones)
      const keysToClear = keys.filter(key => !essentialKeys.includes(key));

      if (keysToClear.length > 0) {
        await AsyncStorage.multiRemove(keysToClear);
        console.log(`CacheManager: Cleared ${keysToClear.length} cache items`);
      }

      // Clear Apollo cache if available
      this.clearApolloCache();
      
      // Clear any file system cache if needed
      await this.clearFileSystemCache();
      
    } catch (error) {
      console.error('CacheManager: Error clearing cache:', error);
    }
  }

  /**
   * Clear Apollo GraphQL cache
   */
  static clearApolloCache(): void {
    try {
      // Apollo cache will be cleared by the client itself
      // This is just a placeholder for future implementation if needed
      console.log('CacheManager: Apollo cache clear requested');
    } catch (error) {
      console.error('CacheManager: Error clearing Apollo cache:', error);
    }
  }

  /**
   * Clear file system cache (temporary files, etc.)
   */
  private static async clearFileSystemCache(): Promise<void> {
    try {
      // Import react-native-fs dynamically to avoid issues
      const RNFS = require('react-native-fs');
      
      // Clear temporary files
      const tempDir = RNFS.TemporaryDirectoryPath;
      const tempFiles = await RNFS.readDir(tempDir);
      
      for (const file of tempFiles) {
        if (file.name.startsWith('shared_image_') || file.name.startsWith('temp_')) {
          try {
            await RNFS.unlink(file.path);
          } catch (error) {
            console.log(`CacheManager: Could not delete temp file ${file.name}:`, error);
          }
        }
      }
      
      console.log('CacheManager: File system cache cleared');
    } catch (error) {
      console.error('CacheManager: Error clearing file system cache:', error);
    }
  }

  /**
   * Force clear cache (for manual cache clearing)
   */
  static async forceClearCache(): Promise<void> {
    try {
      await this.clearAllCache();
      console.log('CacheManager: Force cache clear completed');
    } catch (error) {
      console.error('CacheManager: Error in force clear cache:', error);
    }
  }

  /**
   * Get current app version
   */
  static getCurrentVersion(): string {
    return getVersion();
  }

  /**
   * Get stored app version
   */
  static async getStoredVersion(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(VERSION_KEY);
    } catch (error) {
      console.error('CacheManager: Error getting stored version:', error);
      return null;
    }
  }
}

export default CacheManager; 